'use client';

import { memo, useMemo } from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';

interface OptimizedMarkdownProps {
  content: string;
  className?: string;
}

const OptimizedMarkdown = memo(function OptimizedMarkdown({ 
  content, 
  className = "prose prose-slate max-w-none" 
}: OptimizedMarkdownProps) {
  // Memoize the markdown components to prevent re-creation on every render
  const markdownComponents = useMemo(() => ({
    a: (props: any) => (
      <a 
        className="text-[#FF6800] hover:underline" 
        target="_blank" 
        rel="noopener noreferrer" 
        {...props}
      >
        {props.children}
      </a>
    ),
    // Optimize other components as needed
    p: (props: any) => <p className="mb-4" {...props} />,
    ul: (props: any) => <ul className="list-disc list-inside mb-4" {...props} />,
    ol: (props: any) => <ol className="list-decimal list-inside mb-4" {...props} />,
    li: (props: any) => <li className="mb-1" {...props} />,
    h1: (props: any) => <h1 className="text-2xl font-bold mb-4" {...props} />,
    h2: (props: any) => <h2 className="text-xl font-semibold mb-3" {...props} />,
    h3: (props: any) => <h3 className="text-lg font-medium mb-2" {...props} />,
    code: (props: any) => (
      <code className="bg-slate-100 px-1 py-0.5 rounded text-sm" {...props} />
    ),
    pre: (props: any) => (
      <pre className="bg-slate-100 p-4 rounded-lg overflow-x-auto mb-4" {...props} />
    ),
  }), []);

  // Memoize the remark plugins array
  const remarkPlugins = useMemo(() => [remarkGfm], []);

  return (
    <div className={className}>
      <ReactMarkdown
        remarkPlugins={remarkPlugins}
        components={markdownComponents}
      >
        {content}
      </ReactMarkdown>
    </div>
  );
});

export default OptimizedMarkdown;

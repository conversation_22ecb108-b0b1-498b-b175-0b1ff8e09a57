'use client';

import { Suspense, use } from 'react';
import SearchResultsContent from '@/components/search/SearchResultsContent';

// Loading component for Suspense fallback
function SearchResultsLoading() {
  return (
    <div className="min-h-screen bg-[#fffefa] flex flex-col">
      {/* Header */}
      <div className="border-b border-slate-200 bg-white/80 backdrop-blur-sm sticky top-0 z-10">
        <div className="container mx-auto px-4 py-3 max-w-6xl">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="w-16 h-8 bg-slate-200 rounded animate-pulse" />
              <div className="flex items-center gap-3">
                <div className="p-2 bg-[#FF6800] rounded-lg">
                  <div className="w-6 h-6 bg-white rounded" />
                </div>
                <div>
                  <div className="w-24 h-6 bg-slate-200 rounded animate-pulse mb-1" />
                  <div className="w-32 h-4 bg-slate-200 rounded animate-pulse" />
                </div>
              </div>
            </div>
            <div className="w-20 h-8 bg-slate-200 rounded animate-pulse" />
          </div>
        </div>
      </div>

      {/* Loading Content */}
      <div className="flex-1 container mx-auto px-4 py-6 max-w-6xl">
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <div className="w-8 h-8 border-4 border-[#FF6800] border-t-transparent rounded-full animate-spin mx-auto mb-4" />
            <p className="text-slate-600">Loading search results...</p>
          </div>
        </div>
      </div>
    </div>
  );
}

export default function SearchResultsPage({ params }: { params: Promise<{ uuid: string }> }) {
  const resolvedParams = use(params);

  return (
    <Suspense fallback={<SearchResultsLoading />}>
      <SearchResultsContent uuid={resolvedParams.uuid} />
    </Suspense>
  );
}

#!/usr/bin/env node

/**
 * Search Performance Testing Script
 * 
 * This script tests the search performance optimizations by:
 * 1. Running multiple search queries
 * 2. Measuring response times
 * 3. Checking cache hit rates
 * 4. Verifying database storage behavior
 */

const fetch = require('node-fetch');

const BASE_URL = process.env.BASE_URL || 'http://localhost:3000';

// Test queries to simulate real usage
const TEST_QUERIES = [
  'beasiswa LPDP',
  'persyaratan pendaftaran',
  'dokumen yang diperlukan',
  'program studi yang didukung',
  'cara mendaftar beasiswa',
  'timeline pendaftaran',
  'kriteria penilaian',
  'wawancara beasiswa'
];

class PerformanceTest {
  constructor() {
    this.results = [];
    this.cacheHits = 0;
    this.totalRequests = 0;
  }

  async measureSearchTime(query, includeSharing = false) {
    const startTime = Date.now();
    
    try {
      const url = includeSharing 
        ? `${BASE_URL}/api/search?share=true`
        : `${BASE_URL}/api/search`;
        
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ query }),
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      const endTime = Date.now();
      const responseTime = endTime - startTime;

      return {
        query,
        responseTime,
        resultCount: data.totalResults || 0,
        hasUuid: !!data.uuid,
        isShared: data.isShared || false,
        success: true
      };
    } catch (error) {
      const endTime = Date.now();
      return {
        query,
        responseTime: endTime - startTime,
        error: error.message,
        success: false
      };
    }
  }

  async runPerformanceTest() {
    console.log('🚀 Starting Search Performance Test');
    console.log('=====================================\n');

    // Test 1: Normal searches (no sharing)
    console.log('📊 Test 1: Normal Search Performance');
    console.log('------------------------------------');
    
    for (const query of TEST_QUERIES) {
      const result = await this.measureSearchTime(query, false);
      this.results.push(result);
      this.totalRequests++;

      if (result.success) {
        const status = result.responseTime < 1500 ? '✅' : result.responseTime < 3000 ? '⚠️' : '❌';
        console.log(`${status} "${query}": ${result.responseTime}ms (${result.resultCount} results)`);
      } else {
        console.log(`❌ "${query}": FAILED - ${result.error}`);
      }

      // Small delay between requests
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    // Test 2: Cache performance (repeat same queries)
    console.log('\n📊 Test 2: Cache Performance');
    console.log('-----------------------------');
    
    for (const query of TEST_QUERIES.slice(0, 4)) {
      const result = await this.measureSearchTime(query, false);
      this.results.push(result);
      this.totalRequests++;

      if (result.success) {
        const isCacheHit = result.responseTime < 500; // Assume cache hit if very fast
        if (isCacheHit) this.cacheHits++;
        
        const status = isCacheHit ? '🚀' : result.responseTime < 1500 ? '✅' : '⚠️';
        console.log(`${status} "${query}": ${result.responseTime}ms ${isCacheHit ? '(cache hit)' : ''}`);
      } else {
        console.log(`❌ "${query}": FAILED - ${result.error}`);
      }

      await new Promise(resolve => setTimeout(resolve, 100));
    }

    // Test 3: Sharing performance
    console.log('\n📊 Test 3: Sharing Performance');
    console.log('-------------------------------');
    
    const sharingQuery = TEST_QUERIES[0];
    const sharingResult = await this.measureSearchTime(sharingQuery, true);
    this.results.push(sharingResult);
    this.totalRequests++;

    if (sharingResult.success) {
      const status = sharingResult.responseTime < 2000 ? '✅' : '⚠️';
      console.log(`${status} "${sharingQuery}" (with sharing): ${sharingResult.responseTime}ms`);
      console.log(`   UUID generated: ${sharingResult.hasUuid ? 'Yes' : 'No'}`);
      console.log(`   Is shared: ${sharingResult.isShared ? 'Yes' : 'No'}`);
    } else {
      console.log(`❌ "${sharingQuery}": FAILED - ${sharingResult.error}`);
    }

    this.printSummary();
  }

  printSummary() {
    console.log('\n📈 Performance Summary');
    console.log('======================');

    const successfulResults = this.results.filter(r => r.success);
    const failedResults = this.results.filter(r => !r.success);

    if (successfulResults.length === 0) {
      console.log('❌ No successful requests to analyze');
      return;
    }

    const responseTimes = successfulResults.map(r => r.responseTime);
    const avgResponseTime = responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length;
    const minResponseTime = Math.min(...responseTimes);
    const maxResponseTime = Math.max(...responseTimes);
    const fastRequests = responseTimes.filter(t => t < 1500).length;
    const slowRequests = responseTimes.filter(t => t > 5000).length;

    console.log(`Total Requests: ${this.totalRequests}`);
    console.log(`Successful: ${successfulResults.length}`);
    console.log(`Failed: ${failedResults.length}`);
    console.log(`Cache Hit Rate: ${((this.cacheHits / this.totalRequests) * 100).toFixed(1)}%`);
    console.log('');
    console.log(`Average Response Time: ${avgResponseTime.toFixed(0)}ms`);
    console.log(`Fastest Response: ${minResponseTime}ms`);
    console.log(`Slowest Response: ${maxResponseTime}ms`);
    console.log(`Fast Requests (<1.5s): ${fastRequests}/${successfulResults.length} (${((fastRequests/successfulResults.length)*100).toFixed(1)}%)`);
    console.log(`Slow Requests (>5s): ${slowRequests}/${successfulResults.length} (${((slowRequests/successfulResults.length)*100).toFixed(1)}%)`);

    // Performance assessment
    console.log('\n🎯 Performance Assessment');
    console.log('-------------------------');
    
    if (avgResponseTime < 1500) {
      console.log('✅ EXCELLENT: Average response time under 1.5s');
    } else if (avgResponseTime < 3000) {
      console.log('⚠️  GOOD: Average response time under 3s');
    } else if (avgResponseTime < 5000) {
      console.log('⚠️  FAIR: Average response time under 5s');
    } else {
      console.log('❌ POOR: Average response time over 5s - optimization needed');
    }

    if (slowRequests === 0) {
      console.log('✅ No slow requests detected');
    } else {
      console.log(`❌ ${slowRequests} slow requests detected - investigate bottlenecks`);
    }

    if (this.cacheHits > 0) {
      console.log(`✅ Cache working: ${this.cacheHits} cache hits detected`);
    } else {
      console.log('⚠️  No cache hits detected - verify caching implementation');
    }

    // Recommendations
    if (avgResponseTime > 1500 || slowRequests > 0) {
      console.log('\n💡 Optimization Recommendations');
      console.log('-------------------------------');
      
      if (avgResponseTime > 3000) {
        console.log('• Check database query performance');
        console.log('• Verify vector search optimization');
        console.log('• Consider reducing search strategies');
      }
      
      if (slowRequests > 0) {
        console.log('• Investigate Gemini API response times');
        console.log('• Check network latency');
        console.log('• Verify embedding cache is working');
      }
      
      if (this.cacheHits === 0) {
        console.log('• Verify search cache implementation');
        console.log('• Check cache duration settings');
        console.log('• Ensure cache keys are consistent');
      }
    }
  }
}

// Run the test
async function main() {
  const test = new PerformanceTest();
  
  try {
    await test.runPerformanceTest();
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = PerformanceTest;

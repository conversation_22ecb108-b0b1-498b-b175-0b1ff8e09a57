#!/usr/bin/env node

/**
 * Search Performance Debugging Script
 * 
 * This script performs detailed performance analysis to identify
 * the exact bottlenecks causing slow search performance.
 */

const fetch = require('node-fetch');

const BASE_URL = process.env.BASE_URL || 'http://localhost:3000';

class PerformanceDebugger {
  constructor() {
    this.results = [];
  }

  async measureDetailedPerformance(query) {
    console.log(`\n🔍 Debugging search performance for: "${query}"`);
    console.log('=' .repeat(60));

    const overallStart = Date.now();
    
    try {
      // Measure network latency
      const pingStart = Date.now();
      const pingResponse = await fetch(`${BASE_URL}/api/health`);
      const pingTime = Date.now() - pingStart;
      console.log(`🌐 Network latency: ${pingTime}ms`);

      // Measure search API call
      const apiStart = Date.now();
      const response = await fetch(`${BASE_URL}/api/search`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ query }),
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      const apiTime = Date.now() - apiStart;
      const overallTime = Date.now() - overallStart;

      console.log(`⏱️  API Response Time: ${apiTime}ms`);
      console.log(`⏱️  Overall Time: ${overallTime}ms`);
      console.log(`📊 Results Found: ${data.totalResults || 0}`);
      console.log(`💾 Cache Status: ${data.isShared !== undefined ? 'Optimized' : 'Legacy'}`);

      // Analyze response structure
      if (data.results && data.results.length > 0) {
        console.log(`📄 First Result: ${data.results[0].title}`);
        console.log(`🎯 Relevance Score: ${data.results[0].relevanceScore || 'N/A'}`);
      }

      // Performance assessment
      this.assessPerformance(apiTime, overallTime, data);

      return {
        query,
        apiTime,
        overallTime,
        resultCount: data.totalResults || 0,
        success: true,
        data
      };

    } catch (error) {
      const overallTime = Date.now() - overallStart;
      console.log(`❌ Error: ${error.message}`);
      console.log(`⏱️  Time to Error: ${overallTime}ms`);

      return {
        query,
        apiTime: overallTime,
        overallTime,
        error: error.message,
        success: false
      };
    }
  }

  assessPerformance(apiTime, overallTime, data) {
    console.log('\n📈 Performance Assessment:');
    
    if (apiTime < 500) {
      console.log('✅ EXCELLENT: API response under 500ms');
    } else if (apiTime < 1000) {
      console.log('✅ GOOD: API response under 1s');
    } else if (apiTime < 2000) {
      console.log('⚠️  FAIR: API response under 2s');
    } else if (apiTime < 5000) {
      console.log('⚠️  SLOW: API response under 5s');
    } else {
      console.log('❌ VERY SLOW: API response over 5s');
    }

    // Identify potential bottlenecks
    console.log('\n🔍 Potential Bottlenecks:');
    
    if (apiTime > 2000) {
      console.log('• Check server logs for detailed timing breakdown');
      console.log('• Verify database query performance');
      console.log('• Check Gemini API response times');
      console.log('• Monitor vector search performance');
    }

    if (overallTime - apiTime > 100) {
      console.log('• Network latency may be contributing to delays');
    }

    if (!data.results || data.results.length === 0) {
      console.log('• No results found - check search strategy effectiveness');
    }
  }

  async runComprehensiveDebug() {
    console.log('🚀 Starting Comprehensive Search Performance Debug');
    console.log('==================================================');

    const testQueries = [
      'beasiswa LPDP',           // Common query
      'test',                    // Short query
      'persyaratan pendaftaran beasiswa luar negeri', // Long query
      'xyz123nonexistent',       // Query with no results
    ];

    for (const query of testQueries) {
      const result = await this.measureDetailedPerformance(query);
      this.results.push(result);
      
      // Wait between requests to avoid rate limiting
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    this.generateSummaryReport();
  }

  generateSummaryReport() {
    console.log('\n📊 COMPREHENSIVE PERFORMANCE SUMMARY');
    console.log('=====================================');

    const successfulResults = this.results.filter(r => r.success);
    const failedResults = this.results.filter(r => !r.success);

    if (successfulResults.length === 0) {
      console.log('❌ No successful requests to analyze');
      return;
    }

    const apiTimes = successfulResults.map(r => r.apiTime);
    const avgApiTime = apiTimes.reduce((a, b) => a + b, 0) / apiTimes.length;
    const minApiTime = Math.min(...apiTimes);
    const maxApiTime = Math.max(...apiTimes);

    console.log(`\n📈 API Performance Metrics:`);
    console.log(`   Average Response Time: ${avgApiTime.toFixed(0)}ms`);
    console.log(`   Fastest Response: ${minApiTime}ms`);
    console.log(`   Slowest Response: ${maxApiTime}ms`);
    console.log(`   Successful Requests: ${successfulResults.length}/${this.results.length}`);

    // Performance categorization
    const excellent = apiTimes.filter(t => t < 500).length;
    const good = apiTimes.filter(t => t >= 500 && t < 1000).length;
    const fair = apiTimes.filter(t => t >= 1000 && t < 2000).length;
    const slow = apiTimes.filter(t => t >= 2000 && t < 5000).length;
    const verySlow = apiTimes.filter(t => t >= 5000).length;

    console.log(`\n🎯 Performance Distribution:`);
    console.log(`   Excellent (<500ms): ${excellent}/${successfulResults.length}`);
    console.log(`   Good (500ms-1s): ${good}/${successfulResults.length}`);
    console.log(`   Fair (1s-2s): ${fair}/${successfulResults.length}`);
    console.log(`   Slow (2s-5s): ${slow}/${successfulResults.length}`);
    console.log(`   Very Slow (>5s): ${verySlow}/${successfulResults.length}`);

    // Detailed analysis
    console.log(`\n🔍 Detailed Analysis:`);
    
    if (verySlow > 0) {
      console.log(`❌ CRITICAL: ${verySlow} requests took over 5 seconds`);
      console.log(`   This indicates a serious performance bottleneck`);
      console.log(`   Check server logs for detailed timing breakdown`);
    }

    if (slow > 0) {
      console.log(`⚠️  WARNING: ${slow} requests took 2-5 seconds`);
      console.log(`   Performance optimizations are needed`);
    }

    if (avgApiTime > 1500) {
      console.log(`⚠️  Average response time (${avgApiTime.toFixed(0)}ms) exceeds target (<1500ms)`);
    } else {
      console.log(`✅ Average response time (${avgApiTime.toFixed(0)}ms) meets target`);
    }

    // Recommendations
    console.log(`\n💡 Optimization Recommendations:`);
    
    if (verySlow > 0 || avgApiTime > 3000) {
      console.log(`   🔧 HIGH PRIORITY:`);
      console.log(`      • Check database query performance`);
      console.log(`      • Verify Gemini API response times`);
      console.log(`      • Monitor vector search optimization`);
      console.log(`      • Check for memory/CPU bottlenecks`);
    }

    if (slow > 0 || avgApiTime > 1500) {
      console.log(`   🔧 MEDIUM PRIORITY:`);
      console.log(`      • Implement query result caching`);
      console.log(`      • Optimize search strategies`);
      console.log(`      • Consider database indexing`);
    }

    if (failedResults.length > 0) {
      console.log(`   🔧 ERROR HANDLING:`);
      console.log(`      • ${failedResults.length} requests failed`);
      console.log(`      • Check error logs for details`);
    }

    // Individual query analysis
    console.log(`\n📋 Individual Query Performance:`);
    this.results.forEach((result, index) => {
      const status = result.success 
        ? (result.apiTime < 1500 ? '✅' : result.apiTime < 3000 ? '⚠️' : '❌')
        : '❌';
      
      console.log(`   ${status} "${result.query}": ${result.apiTime}ms ${result.success ? `(${result.resultCount} results)` : `(${result.error})`}`);
    });
  }
}

// Health check endpoint for network latency testing
async function createHealthEndpoint() {
  // This would typically be in your API routes
  console.log('💡 Note: Add a /api/health endpoint for network latency testing');
  console.log('   Example: return NextResponse.json({ status: "ok", timestamp: Date.now() })');
}

// Run the debug analysis
async function main() {
  const debugger = new PerformanceDebugger();
  
  try {
    await debugger.runComprehensiveDebug();
  } catch (error) {
    console.error('❌ Debug analysis failed:', error.message);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = PerformanceDebugger;

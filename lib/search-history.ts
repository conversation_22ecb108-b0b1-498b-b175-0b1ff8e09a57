import { supabase } from './supabase';
import { v4 as uuidv4 } from 'uuid';

export interface SearchHistoryEntry {
  id: string;
  query: string;
  timestamp: string;
  user_id?: string;
  session_id?: string;
}

// Generate a session ID for anonymous users
function getSessionId(): string {
  // Check if we're in a browser environment
  if (typeof window !== 'undefined' && typeof localStorage !== 'undefined') {
    let sessionId = localStorage.getItem('search_session_id');
    if (!sessionId) {
      sessionId = uuidv4(); // Generate a proper UUID
      localStorage.setItem('search_session_id', sessionId);
    }
    return sessionId;
  } else {
    // Server-side: generate a proper UUID for temporary session
    return uuidv4();
  }
}

// Save a search query to the database
export async function saveSearchQuery(query: string, userId?: string): Promise<void> {
  try {
    // Don't save empty queries or system queries
    if (!query || query.trim().length === 0 || query.startsWith('__')) {
      return;
    }

    const trimmedQuery = query.trim().toLowerCase();

    // Don't save very short queries (less than 2 characters)
    if (trimmedQuery.length < 2) {
      return;
    }

    // For anonymous users, don't use session_id since it requires a foreign key
    // Instead, we'll rely on user_id being null to identify anonymous searches
    const sessionId = userId ? null : null; // Set to null for anonymous users

    // Insert the search query into chat_messages table
    // We'll use this table to store search history alongside chat messages
    const { error } = await supabase
      .from('chat_messages')
      .insert({
        content: trimmedQuery,  // Fixed: use 'content' instead of 'message'
        role: 'user',           // Fixed: use 'role' instead of 'sender'
        user_id: userId || null,
        session_id: sessionId,  // Always null for now to avoid foreign key issues
        created_at: new Date().toISOString(),
        // Mark this as a search query for easy filtering
        metadata: { type: 'search_query' }
      });

    if (error) {
      console.error('Error saving search query:', {
        error: error.message,
        details: error.details,
        hint: error.hint,
        code: error.code,
        query: trimmedQuery,
        userId,
        sessionId
      });
    } else {
      console.log('✅ Search query saved successfully:', trimmedQuery);
    }
  } catch (error) {
    console.error('Error saving search query (catch block):', {
      error: error instanceof Error ? error.message : error,
      stack: error instanceof Error ? error.stack : undefined,
      query
    });
  }
}

// Get recent search queries for suggestions
export async function getRecentSearchQueries(limit: number = 10): Promise<string[]> {
  try {
    // Get recent search queries from all users (last 24 hours)
    // Since we're not using session_id for anonymous users, we'll get general recent searches
    const oneDayAgo = new Date();
    oneDayAgo.setDate(oneDayAgo.getDate() - 1);

    // Get recent search queries from all users (last 24 hours)
    const { data, error } = await supabase
      .from('chat_messages')
      .select('content, created_at, metadata')
      .eq('role', 'user')
      .not('metadata', 'is', null)
      .gte('created_at', oneDayAgo.toISOString())
      .order('created_at', { ascending: false })
      .limit(limit * 2); // Get more to filter duplicates

    if (error) {
      console.error('Error fetching recent searches:', {
        error: error.message,
        details: error.details,
        hint: error.hint,
        code: error.code
      });
      return [];
    }

    if (!data || data.length === 0) {
      console.log('No recent search queries found');
      return [];
    }

    // Filter for search queries in JavaScript
    const searchQueries = data.filter(item => {
      return item.metadata &&
             typeof item.metadata === 'object' &&
             item.metadata.type === 'search_query';
    });

    // Remove duplicates and filter out very short queries
    const uniqueQueries = Array.from(new Set(
      searchQueries
        .map(item => item.content)
        .filter(query => query && query.length >= 2)
    )).slice(0, limit);

    return uniqueQueries;
  } catch (error) {
    console.error('Error fetching recent searches (catch block):', {
      error: error instanceof Error ? error.message : error,
      stack: error instanceof Error ? error.stack : undefined
    });
    return [];
  }
}

// Get popular search queries across all users
export async function getPopularSearchQueries(limit: number = 8): Promise<string[]> {
  try {
    // Get popular search queries from all users (last 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const { data, error } = await supabase
      .from('chat_messages')
      .select('content, metadata')
      .eq('role', 'user')
      .not('metadata', 'is', null)
      .gte('created_at', thirtyDaysAgo.toISOString())
      .order('created_at', { ascending: false })
      .limit(200); // Get a larger sample to analyze

    if (error) {
      console.error('Error fetching popular searches:', {
        error: error.message,
        details: error.details,
        hint: error.hint,
        code: error.code
      });
      return getDefaultSuggestions();
    }

    if (!data || data.length === 0) {
      console.log('No popular search queries found, returning defaults');
      return getDefaultSuggestions();
    }

    // Filter for search queries in JavaScript
    const searchQueries = data.filter(item => {
      return item.metadata &&
             typeof item.metadata === 'object' &&
             item.metadata.type === 'search_query';
    });

    if (searchQueries.length === 0) {
      return getDefaultSuggestions();
    }

    // Count frequency of each query
    const queryCount: { [key: string]: number } = {};
    searchQueries.forEach(item => {
      const query = item.content?.toLowerCase().trim();
      if (query && query.length >= 2) {
        queryCount[query] = (queryCount[query] || 0) + 1;
      }
    });

    // Sort by frequency and return top queries
    const popularQueries = Object.entries(queryCount)
      .sort(([, a], [, b]) => b - a)
      .slice(0, limit)
      .map(([query]) => query);

    // If we don't have enough popular queries, supplement with defaults
    if (popularQueries.length < limit) {
      const defaults = getDefaultSuggestions();
      const combined = [...popularQueries];
      
      for (const defaultQuery of defaults) {
        if (!combined.includes(defaultQuery.toLowerCase()) && combined.length < limit) {
          combined.push(defaultQuery.toLowerCase());
        }
      }
      
      return combined.slice(0, limit);
    }

    return popularQueries;
  } catch (error) {
    console.error('Error fetching popular searches (catch block):', {
      error: error instanceof Error ? error.message : error,
      stack: error instanceof Error ? error.stack : undefined
    });
    return getDefaultSuggestions();
  }
}

// Default search suggestions when no history is available
function getDefaultSuggestions(): string[] {
  return [
    'cara daftar LPDP',
    'syarat beasiswa LPDP',
    'jadwal pendaftaran',
    'dokumen yang diperlukan',
    'universitas tujuan',
    'mata garuda',
    'beasiswa reguler',
    'beasiswa afirmasi'
  ];
}

// Interface for intelligent search suggestions
export interface SearchSuggestion {
  query: string;
  category: string;
  relevanceScore: number;
  frequency: number;
}

// Intelligent search suggestions that synthesize recent and popular searches
export async function getIntelligentSearchSuggestions(limit: number = 4): Promise<string[]> {
  try {
    // Get both recent and popular search data
    const [recentQueries, popularQueries] = await Promise.all([
      getRecentSearchQueries(20), // Get more data for analysis
      getPopularSearchQueries(20)
    ]);

    // Combine and analyze all queries
    const allQueries = [...recentQueries, ...popularQueries];

    if (allQueries.length === 0) {
      return getDefaultSuggestions().slice(0, limit);
    }

    // Analyze and synthesize suggestions
    const suggestions = await synthesizeSearchSuggestions(allQueries, limit);

    // If we don't have enough intelligent suggestions, supplement with defaults
    if (suggestions.length < limit) {
      const defaults = getDefaultSuggestions();
      const combined = [...suggestions];

      for (const defaultQuery of defaults) {
        if (!combined.includes(defaultQuery.toLowerCase()) && combined.length < limit) {
          combined.push(defaultQuery.toLowerCase());
        }
      }

      return combined.slice(0, limit);
    }

    return suggestions;
  } catch (error) {
    console.error('Error generating intelligent search suggestions:', error);
    return getDefaultSuggestions().slice(0, limit);
  }
}

// Synthesize intelligent suggestions from search history data
async function synthesizeSearchSuggestions(queries: string[], limit: number): Promise<string[]> {
  // Count frequency of each query
  const queryFrequency: { [key: string]: number } = {};
  queries.forEach(query => {
    const normalizedQuery = query.toLowerCase().trim();
    if (normalizedQuery.length >= 2) {
      queryFrequency[normalizedQuery] = (queryFrequency[normalizedQuery] || 0) + 1;
    }
  });

  // Extract keywords and themes from queries
  const themes = extractThemesFromQueries(Object.keys(queryFrequency));

  // Generate contextual suggestions based on themes and frequency
  const suggestions = generateContextualSuggestions(themes, queryFrequency, limit);

  return suggestions;
}

// Extract common themes and topics from search queries
function extractThemesFromQueries(queries: string[]): { [theme: string]: string[] } {
  const themes: { [theme: string]: string[] } = {
    'pendaftaran': [],
    'syarat': [],
    'dokumen': [],
    'jadwal': [],
    'universitas': [],
    'beasiswa': [],
    'program': []
  };

  // Keywords that indicate specific themes
  const themeKeywords = {
    'pendaftaran': ['daftar', 'pendaftaran', 'registrasi', 'apply', 'application'],
    'syarat': ['syarat', 'requirement', 'kriteria', 'kualifikasi', 'eligibility'],
    'dokumen': ['dokumen', 'document', 'berkas', 'file', 'surat', 'sertifikat'],
    'jadwal': ['jadwal', 'schedule', 'timeline', 'deadline', 'tanggal', 'waktu'],
    'universitas': ['universitas', 'university', 'kampus', 'college', 'sekolah'],
    'beasiswa': ['beasiswa', 'scholarship', 'bantuan', 'funding', 'grant'],
    'program': ['program', 'jenis', 'type', 'kategori', 'reguler', 'afirmasi']
  };

  queries.forEach(query => {
    Object.entries(themeKeywords).forEach(([theme, keywords]) => {
      if (keywords.some(keyword => query.includes(keyword))) {
        themes[theme].push(query);
      }
    });
  });

  return themes;
}

// Generate contextual suggestions based on themes and frequency
function generateContextualSuggestions(
  themes: { [theme: string]: string[] },
  queryFrequency: { [key: string]: number },
  limit: number
): string[] {
  const suggestions: Array<{ query: string; score: number }> = [];

  // Generate suggestions for each theme
  Object.entries(themes).forEach(([theme, queries]) => {
    if (queries.length > 0) {
      // Find the most frequent query in this theme
      const themeQueries = queries.map(query => ({
        query,
        frequency: queryFrequency[query] || 1
      })).sort((a, b) => b.frequency - a.frequency);

      if (themeQueries.length > 0) {
        const topQuery = themeQueries[0];

        // Generate a more general, actionable suggestion based on the theme
        const suggestion = generateThemeSuggestion(theme, topQuery.query);
        if (suggestion) {
          suggestions.push({
            query: suggestion,
            score: topQuery.frequency + (themeQueries.length * 0.1) // Boost score based on theme popularity
          });
        }
      }
    }
  });

  // Sort by score and return top suggestions
  return suggestions
    .sort((a, b) => b.score - a.score)
    .slice(0, limit)
    .map(s => s.query);
}

// Generate a meaningful suggestion based on theme and example query
function generateThemeSuggestion(theme: string, exampleQuery: string): string | null {
  const suggestionTemplates: { [theme: string]: string[] } = {
    'pendaftaran': [
      'cara daftar beasiswa LPDP',
      'proses pendaftaran LPDP',
      'langkah-langkah mendaftar LPDP'
    ],
    'syarat': [
      'syarat beasiswa LPDP',
      'kriteria penerima LPDP',
      'kualifikasi beasiswa LPDP'
    ],
    'dokumen': [
      'dokumen yang diperlukan LPDP',
      'berkas pendaftaran LPDP',
      'persyaratan dokumen LPDP'
    ],
    'jadwal': [
      'jadwal pendaftaran LPDP',
      'timeline beasiswa LPDP',
      'kapan buka pendaftaran LPDP'
    ],
    'universitas': [
      'universitas tujuan LPDP',
      'daftar kampus LPDP',
      'pilihan universitas LPDP'
    ],
    'beasiswa': [
      'jenis beasiswa LPDP',
      'program beasiswa LPDP',
      'kategori beasiswa LPDP'
    ],
    'program': [
      'program reguler LPDP',
      'beasiswa afirmasi LPDP',
      'jenis program LPDP'
    ]
  };

  const templates = suggestionTemplates[theme];
  if (!templates || templates.length === 0) {
    return null;
  }

  // Return the first template for now (could be made more intelligent)
  return templates[0];
}

// Clean up old search history (optional maintenance function)
export async function cleanupOldSearchHistory(daysToKeep: number = 90): Promise<void> {
  try {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

    const { error } = await supabase
      .from('chat_messages')
      .delete()
      .eq('metadata->>type', 'search_query') // Fixed: use ->> for text extraction
      .lt('created_at', cutoffDate.toISOString());

    if (error) {
      console.error('Error cleaning up old search history:', {
        error: error.message,
        details: error.details,
        hint: error.hint,
        code: error.code
      });
    } else {
      console.log(`✅ Cleaned up search history older than ${daysToKeep} days`);
    }
  } catch (error) {
    console.error('Error cleaning up old search history (catch block):', {
      error: error instanceof Error ? error.message : error,
      stack: error instanceof Error ? error.stack : undefined
    });
  }
}

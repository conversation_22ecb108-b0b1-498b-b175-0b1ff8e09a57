import { NextRequest, NextResponse } from 'next/server';
import { OpenRouterClient } from '@/lib/openrouter';
import { SupabaseVectorStore } from '@/lib/supabase-vector-store';
import { SecurityUtils, SECURITY_CONFIG } from '@/lib/security-config';
import { saveSearchQuery } from '@/lib/search-history';
import { supabase } from '@/lib/supabase';
import { v4 as uuidv4 } from 'uuid';

export const dynamic = 'force-dynamic';

// Get client IP for logging
function getClientIP(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for');
  const realIP = request.headers.get('x-real-ip');
  const cfConnectingIP = request.headers.get('cf-connecting-ip');

  if (cfConnectingIP) return cfConnectingIP;
  if (realIP) return realIP;
  if (forwarded) return forwarded.split(',')[0].trim();

  return 'unknown';
}

export async function POST(request: NextRequest) {
  const apiStartTime = Date.now();
  const clientIP = getClientIP(request);

  try {
    const { query } = await request.json();

    // Input validation and sanitization
    if (!query || typeof query !== 'string') {
      SecurityUtils.logSecurityEvent({
        type: 'suspicious_activity',
        ip: clientIP,
        endpoint: '/api/search',
        details: { reason: 'Invalid query format', query: typeof query }
      });

      return NextResponse.json(
        { error: 'Invalid query format' },
        { status: 400 }
      );
    }

    // Sanitize input
    const sanitizedQuery = SecurityUtils.sanitizeInput(query);

    if (sanitizedQuery.length === 0) {
      return NextResponse.json(
        { error: 'Query cannot be empty' },
        { status: 400 }
      );
    }

    if (sanitizedQuery.length > SECURITY_CONFIG.CONTENT.MAX_QUERY_LENGTH) {
      return NextResponse.json(
        { error: `Query too long. Maximum ${SECURITY_CONFIG.CONTENT.MAX_QUERY_LENGTH} characters allowed.` },
        { status: 400 }
      );
    }

    console.log(`🔍 Processing search query: "${sanitizedQuery}"`);

    // Save search query to history (async, don't wait for completion)
    saveSearchQuery(sanitizedQuery).catch(error => {
      console.error('Error saving search query to history:', error);
    });

    // Initialize Supabase vector store with admin access (no user filtering)
    const vectorStore = new SupabaseVectorStore();

    // Track document fetching time
    const docFetchStart = Date.now();
    const allDocuments = await vectorStore.getAllDocuments();
    const docFetchTime = Date.now() - docFetchStart;
    console.log(`📄 Document fetch time: ${docFetchTime}ms (${allDocuments.length} documents)`);
    
    if (allDocuments.length === 0) {
      const emptyResponseData = {
        results: [],
        totalResults: 0,
        query: sanitizedQuery,
        message: "No knowledge base sources have been added yet. The knowledge base is currently empty.",
        suggestions: [],
        timestamp: new Date().toISOString()
      };

      // Store empty search results for sharing
      const resultUuid = uuidv4();
      supabase
        .from('search_results')
        .insert({
          id: resultUuid,
          query: sanitizedQuery,
          results: emptyResponseData,
          user_id: null,
          expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
        })
        .then(({ error: insertError }) => {
          if (insertError) {
            console.error('Error storing empty search results:', insertError);
          }
        });

      return NextResponse.json({
        ...emptyResponseData,
        uuid: resultUuid
      });
    }

    // Search for relevant documents using enhanced vector similarity
    const searchStart = Date.now();
    const searchResults = await vectorStore.searchWithKeywords(sanitizedQuery, 10, 0.02);
    const searchTime = Date.now() - searchStart;
    console.log(`🔍 Vector search time: ${searchTime}ms (${searchResults.length} results)`);
    
    if (searchResults.length === 0) {
      // Get available information for user guidance
      const stats = await vectorStore.getStats();

      console.log(`❌ No search results found for: "${sanitizedQuery}"`);
      console.log(`📋 Available topics: ${stats.topics.slice(0, 10).join(', ')}`);
      console.log(`📄 Available sources: ${stats.sources.join(', ')}`);

      const noResultsData = {
        results: [],
        totalResults: 0,
        query: sanitizedQuery,
        message: `No results found for "${sanitizedQuery}". Try searching for one of these topics instead.`,
        suggestions: stats.topics.slice(0, 8),
        availableSources: stats.sources.slice(0, 5),
        timestamp: new Date().toISOString()
      };

      // Store no-results search for sharing
      const resultUuid = uuidv4();
      supabase
        .from('search_results')
        .insert({
          id: resultUuid,
          query: sanitizedQuery,
          results: noResultsData,
          user_id: null,
          expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
        })
        .then(({ error: insertError }) => {
          if (insertError) {
            console.error('Error storing no-results search:', insertError);
          }
        });

      return NextResponse.json({
        ...noResultsData,
        uuid: resultUuid
      });
    }

    // Format search results for structured display
    const formattedResults = searchResults.map((result, index) => ({
      id: `result-${index}`,
      title: result.document.metadata.title,
      content: result.document.content,
      score: result.score,
      matchType: result.matchType,
      source: result.document.metadata.source,
      sourceUrl: result.document.metadata.sourceUrl,
      type: result.document.metadata.type,
      topics: result.document.metadata.topics,
      addedAt: result.document.metadata.addedAt,
      relevanceScore: Math.round(result.score * 100)
    }));

    // Collect unique sources and source URLs
    const sources = [...new Set(searchResults.map(result => result.document.metadata.title))];
    const sourceUrls: { [title: string]: string } = {};
    
    searchResults.forEach(result => {
      const title = result.document.metadata.title;
      const url = result.document.metadata.sourceUrl;
      if (url) {
        sourceUrls[title] = url;
      }
    });

    console.log(`✅ Found ${searchResults.length} relevant documents`);
    console.log(`📄 Sources: ${sources.join(', ')}`);

    // Generate AI summary if OpenRouter is available
    let aiSummary = null;
    const openRouterApiKey = process.env.OPENROUTER_API_KEY;

    if (openRouterApiKey && searchResults.length > 0) {
      try {
        const aiStart = Date.now();
        const context = searchResults.slice(0, 3).map(result =>
          `Source: ${result.document.metadata.title}\nContent: ${result.document.content}`
        ).join('\n\n');

        const openRouter = new OpenRouterClient(openRouterApiKey);
        aiSummary = await openRouter.generateResponse(sanitizedQuery, context);
        const aiTime = Date.now() - aiStart;
        console.log(`🤖 AI summary generation time: ${aiTime}ms`);
      } catch (aiError) {
        console.error('OpenRouter API error:', aiError);
        // Continue without AI summary
      }
    }

    const embeddingType = await vectorStore.getStats().then(stats => stats.embeddingType);

    // Prepare the response data
    const responseData = {
      results: formattedResults,
      totalResults: searchResults.length,
      query: sanitizedQuery,
      aiSummary,
      sources: sources,
      sourceUrls: Object.keys(sourceUrls).length > 0 ? sourceUrls : undefined,
      searchScores: searchResults.map(r => (r.score * 100).toFixed(1)),
      embeddingType,
      timestamp: new Date().toISOString()
    };

    // Check if sharing is requested via query parameter
    const url = new URL(request.url);
    const enableSharing = url.searchParams.get('share') === 'true';

    if (enableSharing) {
      // Only store in database when sharing is explicitly requested
      const resultUuid = uuidv4();
      supabase
        .from('search_results')
        .insert({
          id: resultUuid,
          query: sanitizedQuery,
          results: responseData,
          user_id: null,
          expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
        })
        .then(({ error: insertError }) => {
          if (insertError) {
            console.error('Error storing search results for sharing:', insertError);
          } else {
            console.log(`✅ Search results stored for sharing with UUID: ${resultUuid}`);
          }
        });

      return NextResponse.json({
        ...responseData,
        uuid: resultUuid,
        isShared: true
      });
    }

    // Log total API processing time
    const totalApiTime = Date.now() - apiStartTime;
    console.log(`⏱️  Total API processing time: ${totalApiTime}ms`);

    // Return immediate response without database storage for faster performance
    return NextResponse.json({
      ...responseData,
      isShared: false
    });
    
  } catch (error) {
    console.error('Search API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const query = searchParams.get('q');

  if (!query) {
    return NextResponse.json(
      { error: 'Query parameter "q" is required' },
      { status: 400 }
    );
  }

  // Convert GET request to POST format
  return POST(new NextRequest(request.url, {
    method: 'POST',
    headers: request.headers,
    body: JSON.stringify({ query })
  }));
}

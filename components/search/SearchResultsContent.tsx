'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { AuthService } from '@/lib/auth';
import { saveSearchQuery } from '@/lib/search-history';
import { searchCache } from '@/lib/search-cache';
import Header from '@/components/layout/Header';
import SearchBox from '@/components/search/SearchBox';
import SearchResults from '@/components/search/SearchResults';
import SearchResultsSkeleton from '@/components/search/SearchResultsSkeleton';
import SearchTransition from '@/components/search/SearchTransition';
import { Share2, ExternalLink, Clock, Users } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface SearchResultsContentProps {
  uuid: string;
}

interface SearchResponse {
  results: any[];
  totalResults: number;
  query: string;
  aiSummary?: string;
  sources?: string[];
  sourceUrls?: { [key: string]: string };
  searchScores?: string[];
  embeddingType?: string;
  timestamp: string;
  uuid?: string;
  originalQuery?: string;
  createdAt?: string;
  accessCount?: number;
  isSharedResult?: boolean;
  message?: string;
  suggestions?: string[];
  availableSources?: string[];
}

export default function SearchResultsContent({ uuid }: SearchResultsContentProps) {
  const [searchResponse, setSearchResponse] = useState<SearchResponse | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [user, setUser] = useState<any>(null);
  const router = useRouter();

  useEffect(() => {
    fetchSearchResults();

    // Check authentication status
    const checkAuth = async () => {
      const currentUser = await AuthService.getCurrentUser();
      setUser(currentUser);
    };
    checkAuth();

    // Listen for auth changes
    const { data: { subscription } } = AuthService.onAuthStateChange((user) => {
      setUser(user);
    });

    return () => subscription.unsubscribe();
  }, [uuid]);

  const fetchSearchResults = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Check if we have this result cached by UUID
      const cachedByUuid = Array.from(searchCache['cache'].values()).find(
        cached => cached.uuid === uuid
      );

      if (cachedByUuid) {
        setSearchResponse({
          ...cachedByUuid.data,
          uuid,
          isSharedResult: true
        });
        setIsLoading(false);
        return;
      }

      const response = await fetch(`/api/search/results/${uuid}`);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to load search results');
      }

      const data = await response.json();
      setSearchResponse(data);

      // Cache the result for future use
      if (data.originalQuery) {
        searchCache.set(data.originalQuery, data, uuid);
      }
    } catch (error) {
      console.error('Error fetching search results:', error);
      setError(error instanceof Error ? error.message : 'Failed to load search results');
    } finally {
      setIsLoading(false);
    }
  };

  const handleNewSearch = async (query: string) => {
    // Save search query to history (async, don't wait for completion)
    saveSearchQuery(query, user?.id).catch(error => {
      console.error('Error saving search query to history:', error);
    });

    // Navigate to regular search page
    router.push(`/search?q=${encodeURIComponent(query)}`);
  };

  const handleSourceClick = (url: string) => {
    window.open(url, '_blank', 'noopener,noreferrer');
  };

  const handleShareResults = async () => {
    const shareUrl = `${window.location.origin}/search/results/${uuid}`;
    
    if (navigator.share) {
      try {
        await navigator.share({
          title: `Search Results: ${searchResponse?.originalQuery || searchResponse?.query}`,
          text: `Check out these search results for "${searchResponse?.originalQuery || searchResponse?.query}"`,
          url: shareUrl,
        });
      } catch (error) {
        // User cancelled sharing or sharing failed
        copyToClipboard(shareUrl);
      }
    } else {
      copyToClipboard(shareUrl);
    }
  };

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toast.success('Link copied to clipboard!');
    } catch (error) {
      toast.error('Failed to copy link');
    }
  };

  if (isLoading) {
    return (
      <SearchTransition>
        <div className="min-h-screen bg-[#fffefa] flex flex-col">
          <Header />

          {/* Search Bar Skeleton */}
          <div className="border-b border-slate-200 bg-white">
            <div className="container mx-auto px-4 py-4 max-w-4xl">
              <div className="w-full h-12 bg-slate-200 rounded-full animate-pulse" />
            </div>
          </div>

          <div className="flex-1 container mx-auto px-4 py-6 max-w-6xl">
            <SearchResultsSkeleton showAISummary={true} resultCount={3} />
          </div>
        </div>
      </SearchTransition>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-[#fffefa] flex flex-col">
        <Header />
        
        {/* Search Bar */}
        <div className="border-b border-slate-200 bg-white">
          <div className="container mx-auto px-4 py-4 max-w-4xl">
            <SearchBox
              onSearch={handleNewSearch}
              placeholder="Search for information..."
              size="md"
            />
          </div>
        </div>

        <div className="flex-1 container mx-auto px-4 py-6 max-w-6xl">
          <div className="text-center py-12">
            <div className="max-w-md mx-auto">
              <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <ExternalLink className="w-8 h-8 text-red-600" />
              </div>
              <h2 className="text-xl font-semibold text-slate-800 mb-2">
                Search Results Not Found
              </h2>
              <p className="text-slate-600 mb-6">
                {error}
              </p>
              <Button 
                onClick={() => router.push('/search')}
                className="bg-[#FF6800] hover:bg-[#e55a00]"
              >
                Start New Search
              </Button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <SearchTransition>
      <div className="min-h-screen bg-[#fffefa] flex flex-col">
      <Header />
      
      {/* Search Bar */}
      <div className="border-b border-slate-200 bg-white">
        <div className="container mx-auto px-4 py-4 max-w-4xl">
          <SearchBox
            onSearch={handleNewSearch}
            placeholder="Search for information..."
            initialValue={searchResponse?.originalQuery || searchResponse?.query}
            size="md"
          />
        </div>
      </div>

      {/* Shared Results Info */}
      {searchResponse?.isSharedResult && (
        <div className="bg-blue-50 border-b border-blue-200">
          <div className="container mx-auto px-4 py-3 max-w-6xl">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2 text-blue-700">
                <Share2 className="w-4 h-4" />
                <span className="text-sm font-medium">Shared Search Results</span>
                {searchResponse.accessCount && (
                  <span className="text-xs bg-blue-100 px-2 py-1 rounded-full flex items-center gap-1">
                    <Users className="w-3 h-3" />
                    {searchResponse.accessCount} views
                  </span>
                )}
                {searchResponse.createdAt && (
                  <span className="text-xs bg-blue-100 px-2 py-1 rounded-full flex items-center gap-1">
                    <Clock className="w-3 h-3" />
                    {new Date(searchResponse.createdAt).toLocaleDateString()}
                  </span>
                )}
              </div>
              <Button
                onClick={handleShareResults}
                variant="outline"
                size="sm"
                className="text-blue-700 border-blue-300 hover:bg-blue-100"
              >
                <Share2 className="w-4 h-4 mr-2" />
                Share
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Main Content */}
      <div className="flex-1 container mx-auto px-4 py-6 max-w-6xl">
        <SearchResults
          searchResponse={searchResponse}
          onSourceClick={handleSourceClick}
          isSharedResult={true}
        />
      </div>
      </div>
    </SearchTransition>
  );
}

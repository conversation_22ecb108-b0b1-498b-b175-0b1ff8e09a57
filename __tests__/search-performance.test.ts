import { searchCache } from '@/lib/search-cache';
import { searchPerformanceMonitor, SearchOptimizer } from '@/lib/search-performance';

// Mock performance.now for consistent testing
const mockPerformanceNow = jest.fn();
Object.defineProperty(global, 'performance', {
  value: {
    now: mockPerformanceNow,
  },
});

describe('Search Performance Optimizations', () => {
  beforeEach(() => {
    searchCache.clear();
    searchPerformanceMonitor.clearMetrics();
    mockPerformanceNow.mockClear();
  });

  describe('Search Cache Optimizations', () => {
    it('should cache search results with improved duration', () => {
      const query = 'test query';
      const mockData = { results: [], totalResults: 0 };
      
      searchCache.set(query, mockData);
      const cached = searchCache.get(query);
      
      expect(cached).toBeTruthy();
      expect(cached?.data).toEqual(mockData);
    });

    it('should evict oldest entries when cache is full', () => {
      // Fill cache beyond limit
      for (let i = 0; i < 105; i++) {
        searchCache.set(`query-${i}`, { results: [], totalResults: 0 });
      }
      
      // Cache should not exceed max size
      expect(searchCache.size()).toBeLessThanOrEqual(100);
      
      // Oldest entries should be evicted
      expect(searchCache.get('query-0')).toBeNull();
      expect(searchCache.get('query-104')).toBeTruthy();
    });

    it('should handle cache key normalization', () => {
      const query1 = 'Test Query';
      const query2 = 'test query';
      const query3 = '  TEST QUERY  ';
      
      const mockData = { results: [], totalResults: 0 };
      searchCache.set(query1, mockData);
      
      // All variations should hit the same cache entry
      expect(searchCache.get(query2)).toBeTruthy();
      expect(searchCache.get(query3)).toBeTruthy();
    });
  });

  describe('Performance Monitoring', () => {
    it('should track search performance metrics', () => {
      mockPerformanceNow
        .mockReturnValueOnce(1000) // Start time
        .mockReturnValueOnce(1500); // End time

      const query = 'test query';
      
      searchPerformanceMonitor.startSearch(query);
      const metrics = searchPerformanceMonitor.endSearch(query, 5);
      
      expect(metrics).toBeTruthy();
      expect(metrics?.totalResponseTime).toBe(500);
      expect(metrics?.resultCount).toBe(5);
      expect(metrics?.cacheHit).toBe(false);
    });

    it('should track cache hits correctly', () => {
      mockPerformanceNow
        .mockReturnValueOnce(1000)
        .mockReturnValueOnce(1200);

      const query = 'cached query';
      
      searchPerformanceMonitor.startSearch(query);
      searchPerformanceMonitor.markCacheHit();
      const metrics = searchPerformanceMonitor.endSearch(query, 3);
      
      expect(metrics?.cacheHit).toBe(true);
      expect(metrics?.totalResponseTime).toBe(200);
    });

    it('should calculate performance statistics', () => {
      // Add multiple search metrics
      mockPerformanceNow
        .mockReturnValueOnce(1000).mockReturnValueOnce(1500) // 500ms
        .mockReturnValueOnce(2000).mockReturnValueOnce(2300) // 300ms
        .mockReturnValueOnce(3000).mockReturnValueOnce(3800); // 800ms

      // First search - no cache hit
      searchPerformanceMonitor.startSearch('query1');
      searchPerformanceMonitor.endSearch('query1', 5);

      // Second search - cache hit
      searchPerformanceMonitor.startSearch('query2');
      searchPerformanceMonitor.markCacheHit();
      searchPerformanceMonitor.endSearch('query2', 3);

      // Third search - no cache hit
      searchPerformanceMonitor.startSearch('query3');
      searchPerformanceMonitor.endSearch('query3', 8);

      const stats = searchPerformanceMonitor.getPerformanceStats();
      
      expect(stats.totalSearches).toBe(3);
      expect(stats.averageResponseTime).toBeCloseTo(533.33, 1);
      expect(stats.cacheHitRate).toBeCloseTo(33.33, 1);
      expect(stats.averageResultCount).toBeCloseTo(5.33, 1);
    });
  });

  describe('Search Optimizer', () => {
    it('should determine cache usage correctly', () => {
      expect(SearchOptimizer.shouldUseCache('a')).toBe(false);
      expect(SearchOptimizer.shouldUseCache('ab')).toBe(true);
      expect(SearchOptimizer.shouldUseCache('test query')).toBe(true);
    });

    it('should determine preloading correctly', () => {
      expect(SearchOptimizer.shouldPreload('ab')).toBe(false);
      expect(SearchOptimizer.shouldPreload('abc')).toBe(true);
      expect(SearchOptimizer.shouldPreload('test query')).toBe(true);
    });

    it('should select optimal search strategies', () => {
      const shortQuery = SearchOptimizer.getOptimalStrategies('test');
      expect(shortQuery).toContain('vector');
      expect(shortQuery).not.toContain('topic');

      const mediumQuery = SearchOptimizer.getOptimalStrategies('test query');
      expect(mediumQuery).toContain('vector');
      expect(mediumQuery).toContain('topic');

      const longQuery = SearchOptimizer.getOptimalStrategies('this is a long test query');
      expect(longQuery).toContain('vector');
      expect(longQuery).toContain('topic');
      expect(longQuery).toContain('keyword');
    });

    it('should calculate search priority correctly', () => {
      const lowPriority = SearchOptimizer.calculatePriority('test', false);
      const highPriority = SearchOptimizer.calculatePriority('this is a long test query', true);
      
      expect(highPriority).toBeGreaterThan(lowPriority);
    });
  });

  describe('Integration Tests', () => {
    it('should handle complete search flow with caching', () => {
      const query = 'integration test';
      const mockData = { results: [{ id: '1', title: 'Test' }], totalResults: 1 };
      
      // First search - cache miss
      mockPerformanceNow
        .mockReturnValueOnce(1000)
        .mockReturnValueOnce(1800);

      searchPerformanceMonitor.startSearch(query);
      searchCache.set(query, mockData);
      const metrics1 = searchPerformanceMonitor.endSearch(query, 1);
      
      expect(metrics1?.cacheHit).toBe(false);
      expect(metrics1?.totalResponseTime).toBe(800);

      // Second search - cache hit
      mockPerformanceNow
        .mockReturnValueOnce(2000)
        .mockReturnValueOnce(2100);

      searchPerformanceMonitor.startSearch(query);
      const cached = searchCache.get(query);
      if (cached) {
        searchPerformanceMonitor.markCacheHit();
      }
      const metrics2 = searchPerformanceMonitor.endSearch(query, 1);
      
      expect(cached).toBeTruthy();
      expect(metrics2?.cacheHit).toBe(true);
      expect(metrics2?.totalResponseTime).toBe(100);
    });

    it('should maintain performance under load', () => {
      const queries = Array.from({ length: 50 }, (_, i) => `query-${i}`);
      
      queries.forEach((query, index) => {
        mockPerformanceNow
          .mockReturnValueOnce(index * 1000)
          .mockReturnValueOnce(index * 1000 + 500);

        searchPerformanceMonitor.startSearch(query);
        searchCache.set(query, { results: [], totalResults: index });
        searchPerformanceMonitor.endSearch(query, index);
      });

      const stats = searchPerformanceMonitor.getPerformanceStats();
      expect(stats.totalSearches).toBe(50);
      expect(stats.averageResponseTime).toBe(500);
      
      // Cache should maintain reasonable size
      expect(searchCache.size()).toBeLessThanOrEqual(100);
    });
  });
});

// Mock fetch for API testing
global.fetch = jest.fn();

describe('API Performance Optimizations', () => {
  beforeEach(() => {
    (fetch as jest.Mock).mockClear();
  });

  it('should handle conditional database storage', async () => {
    const mockResponse = {
      ok: true,
      json: () => Promise.resolve({
        results: [],
        totalResults: 0,
        query: 'test',
        isShared: false
      })
    };

    (fetch as jest.Mock).mockResolvedValue(mockResponse);

    // Test normal search (no sharing)
    const response1 = await fetch('/api/search', {
      method: 'POST',
      body: JSON.stringify({ query: 'test' })
    });
    
    const data1 = await response1.json();
    expect(data1.isShared).toBe(false);

    // Test search with sharing
    const response2 = await fetch('/api/search?share=true', {
      method: 'POST',
      body: JSON.stringify({ query: 'test' })
    });
    
    expect(fetch).toHaveBeenCalledTimes(2);
  });
});

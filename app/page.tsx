'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  Search,
  Bot,
  Brain,
  Settings,
  Shield,
  LogOut,
  BookOpen,
  Info,
  ExternalLink,
  ArrowUpRight
} from 'lucide-react';
import { toast } from 'sonner';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { AuthService } from '@/lib/auth';
import SearchBox from '@/components/search/SearchBox';
import SearchHistory from '@/components/search/SearchHistory';
import { saveSearchQuery } from '@/lib/search-history';

export default function HomePage() {
  const [user, setUser] = useState<any>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();

  useEffect(() => {
    // Check authentication status
    const checkAuth = async () => {
      const currentUser = await AuthService.getCurrentUser();
      setUser(currentUser);
    };
    checkAuth();

    // Listen for auth changes
    const { data: { subscription } } = AuthService.onAuthStateChange((user) => {
      setUser(user);
    });

    return () => subscription.unsubscribe();
  }, []);





  const handleSearch = async (query: string) => {
    setIsLoading(true);
    try {
      // Save search query to history (async, don't wait for completion)
      saveSearchQuery(query, user?.id).catch(error => {
        console.error('Error saving search query to history:', error);
      });

      // Navigate to search results page with query parameter
      router.push(`/search?q=${encodeURIComponent(query)}`);
    } catch (error) {
      console.error('Error navigating to search:', error);
      toast.error('Failed to perform search. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };



  const handleSignOut = async () => {
    try {
      await AuthService.signOut();
      setUser(null);
      toast.success('Successfully signed out!');
    } catch (error) {
      console.error('Sign out error:', error);
      toast.error('Failed to sign out');
    }
  };




  return (
    <div className="min-h-screen flex flex-col">
      {/* Header */}
      <header className="bg-white border-b border-slate-200">
        <div className="container mx-auto px-4 py-2 flex justify-between items-center">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-[#FF6800] rounded-lg">
                <Bot className="w-6 h-6 text-white" />
              </div>
              <div>
                <h1 className="text-xl font-bold text-slate-800">Tanya LPDP</h1>
                <p className="text-slate-600 text-sm flex items-center gap-2">
                  <Brain className="w-3 h-3" />
                  AI Chatbot dengan sumber terkurasi
                </p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              {user ? (
                <>
                  <Badge variant="outline" className="hidden sm:flex items-center gap-1 text-xs">
                    <Shield className="w-3 h-3" />
                    Authenticated
                  </Badge>
                  <Link href="/admin">
                    <Button size="sm" className="mt-1 text-xs h-7">
                      Kunjungi
                    </Button>
                  </Link>
                  <Button variant="outline" size="sm" onClick={handleSignOut}>
                    <LogOut className="w-4 h-4 mr-2" />
                    Sign Out
                  </Button>
                </>
              ) : null}
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <div className="flex-1 flex flex-col">
        {/* Hero Section */}
        <div className="container mx-auto px-3 py-8 max-w-4xl text-center">
          <div className="mb-6">
            <h2 className="text-2xl md:text-3xl font-bold text-slate-800 mb-3">
              Tanya soal LPDP di sini
            </h2>
          </div>

          {/* Search Form */}
          <div className="mb-4">
            <SearchBox
              onSearch={handleSearch}
              placeholder="Masukkan pertanyaanmu..."
              isLoading={isLoading}
              size="lg"
              className="max-w-2xl mx-auto"
            />
          </div>

          {/* Search History and Suggestions */}
          <div className="mt-8">
            <SearchHistory onSuggestionClick={handleSearch} />
          </div>

          {/* Knowledge Base Info */}
          <div className="max-w-4xl mx-auto mb-4">
            <Card className="border-blue-100 bg-blue-50 shadow-sm">
              <CardContent className="p-2">
                <div className="flex items-start gap-2">
                  <div className="p-1 bg-blue-100 rounded-full flex-shrink-0">
                    <Info className="w-3 h-3 text-blue-600" />
                  </div>
                  <div className="text-left">
                    <p className="text-xs text-slate-600 leading-snug">
                      <strong>Tanya LPDP bukan kanal resmi LPDP.</strong> Kami hanya mengkurasi informasi yang tersedia secara publik agar lebih mudah diakses.
                    </p>
                    <p className="text-xs text-slate-600 leading-snug">
                      Silakan kunjungi kanal resmi di bawah ini untuk informasi lebih lanjut.
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="grid md:grid-cols-2 gap-3 max-w-4xl mx-auto">
            <a 
              href="https://lpdp.kemenkeu.go.id/" 
              target="_blank" 
              rel="noopener noreferrer"
              className="block hover:opacity-90 transition-opacity text-left"
            >
              <Card className="border-slate-200 shadow-sm hover:shadow-md transition-shadow h-full relative group">
                <CardContent className="p-4">
                  <div className="absolute top-3 right-3 text-slate-300 group-hover:text-slate-400 transition-colors">
                    <ArrowUpRight className="w-4 h-4" />
                  </div>
                  <div className="flex items-start gap-4 pr-4">
                    <div className="p-2 bg-blue-100 rounded-lg flex-shrink-0">
                      <BookOpen className="w-5 h-5 text-blue-600" />
                    </div>
                    <div className="text-left">
                      <h4 className="font-semibold text-slate-800 text-sm mb-1">Situs Resmi LPDP</h4>
                      <p className="text-sm text-slate-600">
                        Kanal resmi milik Kemenkeu
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </a>

            <a 
              href="https://matagaruda.org/" 
              target="_blank" 
              rel="noopener noreferrer"
              className="block hover:opacity-90 transition-opacity text-left"
            >
              <Card className="border-slate-200 shadow-sm hover:shadow-md transition-shadow h-full relative group">
                <CardContent className="p-4">
                  <div className="absolute top-3 right-3 text-slate-300 group-hover:text-slate-400 transition-colors">
                    <ArrowUpRight className="w-4 h-4" />
                  </div>
                  <div className="flex items-start gap-4 pr-4">
                    <div className="p-2 bg-green-100 rounded-lg flex-shrink-0">
                      <Brain className="w-5 h-5 text-green-600" />
                    </div>
                    <div className="text-left">
                      <h4 className="font-semibold text-slate-800 text-sm mb-1">Situs Resmi Mata Garuda</h4>
                      <p className="text-sm text-slate-600">
                        Portal alumni LPDP
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </a>
          </div>
        </div>
      </div>

      {/* Footer */}
      <footer className="bg-white border-t border-slate-200 mt-auto">
        <div className="container mx-auto px-4 py-4 max-w-6xl">
          <div className="text-center text-sm text-slate-500">
            <p>
              Dikembangkan oleh{' '}
              <a 
                href="https://bagusfarisa.com" 
                target="_blank" 
                rel="noopener noreferrer"
                className="font-semibold text-slate-700 hover:text-[#FF6800] transition-colors"
              >
                Guntur
              </a>{' '}
              & <strong>Hatta</strong>
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
}

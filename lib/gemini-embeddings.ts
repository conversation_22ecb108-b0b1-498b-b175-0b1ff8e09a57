interface GeminiEmbeddingResponse {
  embedding: {
    values: number[];
  };
}

export class GeminiEmbeddingClient {
  private apiKey: string;
  private baseUrl = 'https://generativelanguage.googleapis.com/v1beta';
  private model = 'models/text-embedding-004'; // Updated to latest Gemini embedding model
  private queryEmbeddingCache: Map<string, { embedding: number[], timestamp: number }>;
  private readonly CACHE_DURATION = 10 * 60 * 1000; // 10 minutes

  constructor(apiKey: string) {
    this.apiKey = apiKey;
    this.queryEmbeddingCache = new Map();
  }

  private async sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  private isRetryableError(error: any): boolean {
    // Check for network errors
    if (error.name === 'TypeError' && error.message.includes('fetch failed')) {
      return true;
    }
    
    // Check for socket errors
    if (error.cause && error.cause.name === 'SocketError') {
      return true;
    }
    
    return false;
  }

  private isRetryableStatus(status: number): boolean {
    // Retry on rate limiting and server errors
    return status === 429 || (status >= 500 && status < 600);
  }

  async generateEmbedding(text: string): Promise<number[]> {
    const maxRetries = 3;
    const baseDelay = 1000; // 1 second

    // Clean and prepare text
    const cleanText = text.replace(/\n+/g, ' ').trim();
    if (cleanText.length === 0) {
      throw new Error('Text cannot be empty');
    }

    // Truncate if too long (text-embedding-004 has higher limits)
    const maxLength = 10000; // Increased limit for the newer model
    const processedText = cleanText.length > maxLength 
      ? cleanText.substring(0, maxLength) + '...'
      : cleanText;

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        const response = await fetch(
          `${this.baseUrl}/${this.model}:embedContent?key=${this.apiKey}`,
          {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              model: this.model,
              content: {
                parts: [{ text: processedText }]
              },
              taskType: 'RETRIEVAL_DOCUMENT', // Optimized for document retrieval
              title: 'Knowledge Base Document' // Optional title for better context
            })
          }
        );

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}));
          
          // Check if this is a retryable HTTP status
          if (this.isRetryableStatus(response.status) && attempt < maxRetries) {
            const delay = baseDelay * Math.pow(2, attempt);
            console.warn(`Gemini API returned ${response.status}, retrying in ${delay}ms (attempt ${attempt + 1}/${maxRetries + 1})`);
            await this.sleep(delay);
            continue;
          }
          
          throw new Error(`Gemini API error: ${response.status} - ${errorData.error?.message || 'Unknown error'}`);
        }

        const data: GeminiEmbeddingResponse = await response.json();
        
        if (!data.embedding || !data.embedding.values) {
          throw new Error('Invalid embedding response from Gemini API');
        }

        return data.embedding.values;
        
      } catch (error) {
        console.error(`Gemini API error (attempt ${attempt + 1}/${maxRetries + 1}):`, error);
        
        // Check if this is a retryable error and we have attempts left
        if (this.isRetryableError(error) && attempt < maxRetries) {
          const delay = baseDelay * Math.pow(2, attempt);
          console.warn(`Retrying Gemini API call in ${delay}ms due to network error`);
          await this.sleep(delay);
          continue;
        }
        
        // If we've exhausted retries or it's not a retryable error, throw
        throw error;
      }
    }

    // This should never be reached, but TypeScript requires it
    throw new Error('Maximum retry attempts exceeded');
  }

  async generateBatchEmbeddings(texts: string[]): Promise<number[][]> {
    const embeddings: number[][] = [];
    
    // Process in batches to avoid rate limits
    const batchSize = 5;
    for (let i = 0; i < texts.length; i += batchSize) {
      const batch = texts.slice(i, i + batchSize);
      const batchPromises = batch.map(text => this.generateEmbedding(text));
      
      try {
        const batchResults = await Promise.all(batchPromises);
        embeddings.push(...batchResults);
        
        // Add delay between batches to respect rate limits
        if (i + batchSize < texts.length) {
          await this.sleep(200); // 200ms delay between batches
        }
      } catch (error) {
        console.error(`Error processing batch starting at index ${i}:`, error);
        throw error;
      }
    }
    
    return embeddings;
  }

  // Generate query embedding optimized for search with caching
  async generateQueryEmbedding(query: string): Promise<number[]> {
    const cleanQuery = query.replace(/\n+/g, ' ').trim();
    if (cleanQuery.length === 0) {
      throw new Error('Query cannot be empty');
    }

    // Check cache first
    const cacheKey = cleanQuery.toLowerCase();
    const cached = this.queryEmbeddingCache.get(cacheKey);
    if (cached && Date.now() - cached.timestamp < this.CACHE_DURATION) {
      return cached.embedding;
    }

    const maxRetries = 3;
    const baseDelay = 1000;

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        const response = await fetch(
          `${this.baseUrl}/${this.model}:embedContent?key=${this.apiKey}`,
          {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              model: this.model,
              content: {
                parts: [{ text: cleanQuery }]
              },
              taskType: 'RETRIEVAL_QUERY' // Optimized for search queries
            })
          }
        );

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}));
          
          if (this.isRetryableStatus(response.status) && attempt < maxRetries) {
            const delay = baseDelay * Math.pow(2, attempt);
            console.warn(`Gemini API returned ${response.status}, retrying in ${delay}ms (attempt ${attempt + 1}/${maxRetries + 1})`);
            await this.sleep(delay);
            continue;
          }
          
          throw new Error(`Gemini API error: ${response.status} - ${errorData.error?.message || 'Unknown error'}`);
        }

        const data: GeminiEmbeddingResponse = await response.json();
        
        if (!data.embedding || !data.embedding.values) {
          throw new Error('Invalid embedding response from Gemini API');
        }

        // Cache the result
        this.queryEmbeddingCache.set(cacheKey, {
          embedding: data.embedding.values,
          timestamp: Date.now()
        });

        // Clean up old cache entries
        this.cleanupCache();

        return data.embedding.values;
        
      } catch (error) {
        console.error(`Gemini query embedding error (attempt ${attempt + 1}/${maxRetries + 1}):`, error);
        
        if (this.isRetryableError(error) && attempt < maxRetries) {
          const delay = baseDelay * Math.pow(2, attempt);
          console.warn(`Retrying Gemini API call in ${delay}ms due to network error`);
          await this.sleep(delay);
          continue;
        }
        
        throw error;
      }
    }

    throw new Error('Maximum retry attempts exceeded');
  }

  // Clean up expired cache entries
  private cleanupCache(): void {
    const now = Date.now();
    for (const [key, value] of this.queryEmbeddingCache.entries()) {
      if (now - value.timestamp > this.CACHE_DURATION) {
        this.queryEmbeddingCache.delete(key);
      }
    }
  }

  // Utility method to chunk large text for embedding
  chunkText(text: string, maxChunkSize: number = 1500, overlap: number = 150): string[] {
    if (text.length <= maxChunkSize) {
      return [text];
    }

    const chunks: string[] = [];
    let start = 0;

    while (start < text.length) {
      let end = start + maxChunkSize;
      
      // Try to break at sentence boundaries
      if (end < text.length) {
        const lastSentence = text.lastIndexOf('.', end);
        const lastNewline = text.lastIndexOf('\n', end);
        const breakPoint = Math.max(lastSentence, lastNewline);
        
        if (breakPoint > start + maxChunkSize * 0.5) {
          end = breakPoint + 1;
        }
      }

      chunks.push(text.substring(start, end).trim());
      start = Math.max(start + maxChunkSize - overlap, end);
    }

    return chunks.filter(chunk => chunk.length > 0);
  }
}
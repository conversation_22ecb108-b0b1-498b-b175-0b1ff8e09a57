'use client';

import { useState, useCallback, memo } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';

import {
  Clock,
  Search,
  Brain,
  Copy,
  Check,
  XCircle,
  Share2,
  TrendingUp,
  ArrowRight
} from 'lucide-react';
import { toast } from 'sonner';
import OptimizedMarkdown from './OptimizedMarkdown';

interface SearchResponse {
  results: any[];
  totalResults: number;
  query: string;
  aiSummary?: string;
  sources?: string[];
  sourceUrls?: { [key: string]: string };
  searchScores?: string[];
  embeddingType?: string;
  timestamp: string;
  uuid?: string;
  originalQuery?: string;
  createdAt?: string;
  accessCount?: number;
  isSharedResult?: boolean;
  message?: string;
  suggestions?: string[];
  availableSources?: string[];
}

interface SearchResultsProps {
  searchResponse: SearchResponse | null;
  onSourceClick: (url: string) => void;
  onSuggestionClick?: (suggestion: string) => void;
  isSharedResult?: boolean;
}

function SearchSuggestions({ onSuggestionClick, knowledgeStats }: { 
  onSuggestionClick: (suggestion: string) => void;
  knowledgeStats?: {
    topics: string[];
    sources: string[];
    totalDocuments: number;
  } 
}) {
  const [popularQueries] = useState([
    'cara daftar LPDP',
    'syarat beasiswa LPDP',
    'jadwal pendaftaran',
    'dokumen yang diperlukan',
  ]);

  return (
    <div className="space-y-6">
      {/* Popular Searches */}
      <Card className="border-slate-200 shadow-sm">
        <CardContent className="p-6">
          <h3 className="font-semibold text-slate-800 mb-4 flex items-center gap-2">
            <TrendingUp className="w-5 h-5 text-[#FF6800]" />
            Coba cari dengan pertanyaan berikut ini
          </h3>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
            {popularQueries.map((query, index) => (
              <Button
                key={index}
                variant="outline"
                size="sm"
                onClick={() => onSuggestionClick(query)}
                className="justify-start text-left h-auto py-2 px-3 hover:border-[#FF6800] hover:text-[#FF6800] transition-colors"
              >
                <span className="truncate">{query}</span>
                <ArrowRight className="w-3 h-3 ml-auto flex-shrink-0" />
              </Button>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

const SearchResults = memo(function SearchResults({
  searchResponse,
  onSourceClick,
  onSuggestionClick = (query: string) => window.location.href = `/search?q=${encodeURIComponent(query)}`,
  isSharedResult = false
}: SearchResultsProps) {
  const [copiedText, setCopiedText] = useState(false);
  const router = useRouter();

  const handleCopyToClipboard = useCallback(async (text: string) => {
    try {
      if (!navigator.clipboard) {
        throw new Error('Clipboard API not available');
      }

      await navigator.clipboard.writeText(text);
      setCopiedText(true);
      setTimeout(() => setCopiedText(false), 2000);
      toast.success('Copied to clipboard!');
    } catch (err) {
      console.error('Failed to copy text: ', err);
      toast.error('Failed to copy text to clipboard');
    }
  }, []);

  const copyToClipboard = useCallback(async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toast.success('Link copied to clipboard!');
    } catch (error) {
      toast.error('Failed to copy link');
    }
  }, []);

  const handleShareResults = useCallback(async () => {
    if (!searchResponse?.uuid) return;

    const shareUrl = `${window.location.origin}/search/results/${searchResponse.uuid}`;

    if (navigator.share) {
      try {
        await navigator.share({
          title: `Search Results: ${searchResponse.originalQuery || searchResponse.query}`,
          text: `Check out these search results for "${searchResponse.originalQuery || searchResponse.query}"`,
          url: shareUrl,
        });
      } catch (error) {
        // User cancelled sharing or sharing failed
        copyToClipboard(shareUrl);
      }
    } else {
      copyToClipboard(shareUrl);
    }
  }, [searchResponse?.uuid, searchResponse?.originalQuery, searchResponse?.query, copyToClipboard]);

  if (!searchResponse) {
    return null;
  }

  // If no results found, show search suggestions
  if (searchResponse.results.length === 0) {
    return (
      <div className="space-y-6">
        <div className="text-center py-8">
          <XCircle className="w-12 h-12 mx-auto text-slate-300 mb-4" />
          <h3 className="text-lg font-medium text-slate-800 mb-2">No results found for "{searchResponse.originalQuery || searchResponse.query}"</h3>
          <p className="text-slate-500 mb-6">Try a different search term or check out these suggestions:</p>
          <SearchSuggestions 
            onSuggestionClick={onSuggestionClick}
            knowledgeStats={searchResponse.availableSources ? {
              topics: [],
              sources: searchResponse.availableSources,
              totalDocuments: 0
            } : undefined}
          />
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Search Info */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4 text-sm text-slate-500">
          <div className="flex items-center gap-1">
            <Clock className="w-4 h-4" />
            <span>Search completed</span>
          </div>
          <div className="flex items-center gap-1">
            <span>{searchResponse.totalResults} {searchResponse.totalResults === 1 ? 'source' : 'sources'} found</span>
          </div>
        </div>
        <div className="flex items-center gap-3">
          <div className="text-sm text-slate-500">
            Query: "{searchResponse.originalQuery || searchResponse.query}"
          </div>
          {searchResponse.uuid && !isSharedResult && (
            <Button
              onClick={handleShareResults}
              variant="outline"
              size="sm"
              className="text-slate-600 hover:text-slate-800"
            >
              <Share2 className="w-4 h-4 mr-2" />
              Share
            </Button>
          )}
        </div>
      </div>

      {/* AI Summary */}
      {searchResponse.aiSummary && (
        <Card className="border-slate-200 shadow-sm bg-gradient-to-r from-orange-50 to-amber-50">
          <CardContent className="p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-2">
                <Brain className="w-5 h-5 text-[#FF6800]" />
                <span className="font-medium text-slate-800">AI Summary</span>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleCopyToClipboard(searchResponse.aiSummary!)}
                className="text-slate-500 hover:text-slate-700"
              >
                {copiedText ? (
                  <>
                    <Check className="w-4 h-4 mr-2 text-green-500" />
                    Copied!
                  </>
                ) : (
                  <>
                    <Copy className="w-4 h-4 mr-2" />
                    Copy
                  </>
                )}
              </Button>
            </div>

            <OptimizedMarkdown content={searchResponse.aiSummary} />
          </CardContent>
        </Card>
      )}

      {/* Search Results */}
      {searchResponse.results.length > 0 && (
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-slate-800 flex items-center gap-2">
            <Search className="w-5 h-5 text-[#FF6800]" />
            Sources ({searchResponse.totalResults})
          </h3>

          <div className="space-y-2">
            {searchResponse.results.map((result) => (
              <div key={result.id}>
                {result.sourceUrl ? (
                  <button
                    className="text-[#FF6800] hover:underline focus:outline-none focus:ring-2 focus:ring-[#FF6800] focus:ring-opacity-50 rounded px-1 py-0.5 text-left"
                    onClick={() => onSourceClick(result.sourceUrl!)}
                    aria-label={`Open source: ${result.title}`}
                  >
                    {result.title}
                  </button>
                ) : (
                  <span className="text-slate-700 px-1 py-0.5">{result.title}</span>
                )}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* No Results */}
      {searchResponse.totalResults === 0 && (
        <div className="text-center py-12">
          <div className="mb-6">
            <XCircle className="w-16 h-16 text-slate-400 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-slate-700 mb-2">No Results Found</h3>
            <p className="text-slate-600 mb-4">
              {searchResponse.message || `No results found for "${searchResponse.query}"`}
            </p>
          </div>

          {searchResponse.suggestions && searchResponse.suggestions.length > 0 && (
            <div className="max-w-2xl mx-auto">
              <h4 className="font-medium text-slate-700 mb-3">Try searching for these topics instead:</h4>
              <div className="flex flex-wrap gap-2 justify-center">
                {searchResponse.suggestions.map((suggestion, index) => (
                  <Button
                    key={index}
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      router.push(`/search?q=${encodeURIComponent(suggestion)}`);
                    }}
                    className="rounded-full"
                  >
                    {suggestion}
                  </Button>
                ))}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
});

export default SearchResults;

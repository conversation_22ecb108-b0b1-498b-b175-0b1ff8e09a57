-- Migration to add search results storage for shareable URLs
-- This creates a new table to store search results with UUIDs for sharing

-- Create search_results table for storing shareable search results
CREATE TABLE IF NOT EXISTS search_results (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  query TEXT NOT NULL,
  results JSONB NOT NULL,
  user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  expires_at TIMESTAMPTZ NOT NULL DEFAULT (NOW() + INTERVAL '24 hours'),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  accessed_count INTEGER DEFAULT 0,
  last_accessed_at TIMESTAMPTZ
);

-- Create index on expires_at for efficient cleanup
CREATE INDEX IF NOT EXISTS idx_search_results_expires_at 
ON search_results (expires_at);

-- Create index on query for potential analytics
CREATE INDEX IF NOT EXISTS idx_search_results_query 
ON search_results (query);

-- Enable <PERSON><PERSON> on search_results table
ALTER TABLE search_results ENABLE ROW LEVEL SECURITY;

-- Policy: Anyone can read non-expired search results (for sharing)
CREATE POLICY "Anyone can read non-expired search results"
  ON search_results FOR SELECT
  TO anon, authenticated
  USING (expires_at > NOW());

-- Policy: Only authenticated users can create search results
CREATE POLICY "Authenticated users can create search results"
  ON search_results FOR INSERT
  TO authenticated
  WITH CHECK (true);

-- Policy: Anonymous users can also create search results (for public sharing)
CREATE POLICY "Anonymous users can create search results"
  ON search_results FOR INSERT
  TO anon
  WITH CHECK (user_id IS NULL);

-- Policy: Users can update their own search results (for access tracking)
CREATE POLICY "Users can update their own search results"
  ON search_results FOR UPDATE
  TO authenticated
  USING (auth.uid() = user_id OR user_id IS NULL);

-- Policy: Anonymous users can update search results for access tracking
CREATE POLICY "Anonymous users can update search results for tracking"
  ON search_results FOR UPDATE
  TO anon
  USING (user_id IS NULL);

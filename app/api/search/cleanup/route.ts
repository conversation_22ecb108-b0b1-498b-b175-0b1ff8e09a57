import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

export async function POST(request: NextRequest) {
  try {
    // This endpoint should be called periodically (e.g., via cron job)
    // to clean up expired search results
    
    const { data: deletedResults, error } = await supabase
      .from('search_results')
      .delete()
      .lt('expires_at', new Date().toISOString())
      .select('id');

    if (error) {
      console.error('Error cleaning up expired search results:', error);
      return NextResponse.json(
        { error: 'Failed to cleanup expired results' },
        { status: 500 }
      );
    }

    const deletedCount = deletedResults?.length || 0;
    console.log(`✅ Cleaned up ${deletedCount} expired search results`);

    return NextResponse.json({
      success: true,
      deletedCount,
      message: `Successfully cleaned up ${deletedCount} expired search results`
    });

  } catch (error) {
    console.error('Cleanup API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Optional: Allow GET requests for manual cleanup
export async function GET(request: NextRequest) {
  return POST(request);
}

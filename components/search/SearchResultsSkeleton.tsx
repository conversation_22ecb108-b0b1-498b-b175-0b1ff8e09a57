'use client';


import { Card, CardContent } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';

interface SearchResultsSkeletonProps {
  showAISummary?: boolean;
  resultCount?: number;
}

export default function SearchResultsSkeleton({
  showAISummary = true,
  resultCount = 3
}: SearchResultsSkeletonProps) {
  return (
    <div className="space-y-6 search-fade-in">
      {/* Search Info Skeleton */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4 text-sm">
          <div className="flex items-center gap-1">
            <Skeleton className="w-4 h-4" />
            <Skeleton className="w-24 h-4" />
          </div>
          <Skeleton className="w-20 h-4" />
        </div>
        <Skeleton className="w-32 h-4" />
      </div>

      {/* AI Summary Skeleton */}
      {showAISummary && (
        <Card className="border-slate-200 shadow-sm bg-gradient-to-r from-orange-50 to-amber-50">
          <CardContent className="p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-2">
                <Skeleton className="w-5 h-5" />
                <Skeleton className="w-24 h-5" />
              </div>
              <Skeleton className="w-16 h-8" />
            </div>
            <div className="space-y-3">
              <Skeleton className="w-full h-4" />
              <Skeleton className="w-full h-4" />
              <Skeleton className="w-3/4 h-4" />
              <Skeleton className="w-full h-4" />
              <Skeleton className="w-5/6 h-4" />
            </div>
          </CardContent>
        </Card>
      )}

      {/* Search Results Header Skeleton */}
      <div className="flex items-center gap-2">
        <Skeleton className="w-5 h-5" />
        <Skeleton className="w-40 h-6" />
      </div>

      {/* Search Results Skeleton */}
      <div className="space-y-2">
        {[...Array(resultCount)].map((_, index) => (
          <div
            key={index}
            className="search-stagger-in"
            style={{
              animationDelay: `${index * 150}ms`,
              opacity: 0,
              animationFillMode: 'forwards'
            }}
          >
            <Skeleton className="w-64 h-6" />
          </div>
        ))}
      </div>
    </div>
  );
}

'use client';

import { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  TrendingUp, 
  BookOpen, 
  GraduationCap, 
  FileText, 
  Globe,
  ArrowRight 
} from 'lucide-react';

interface SearchSuggestionsProps {
  onSuggestionClick: (suggestion: string) => void;
  knowledgeStats?: {
    topics: string[];
    sources: string[];
    totalDocuments: number;
  };
}

export default function SearchSuggestions({ onSuggestionClick, knowledgeStats }: SearchSuggestionsProps) {
  const [popularQueries] = useState([
    'cara daftar LPDP',
    'syarat beasiswa LPDP',
    'jadwal pendaftaran',
    'dokumen yang diperlukan',
  ]);

  const getCategoryIcon = (category: string) => {
    switch (category.toLowerCase()) {
      case 'pendaftaran':
      case 'registration':
        return <FileText className="w-4 h-4" />;
      case 'universitas':
      case 'university':
        return <GraduationCap className="w-4 h-4" />;
      case 'dokumen':
      case 'document':
        return <BookOpen className="w-4 h-4" />;
      case 'website':
      case 'situs':
        return <Globe className="w-4 h-4" />;
      default:
        return <TrendingUp className="w-4 h-4" />;
    }
  };

  return (
    <div className="space-y-6">
      {/* Popular Searches */}
      <Card className="border-slate-200 shadow-sm">
        <CardContent className="p-6">
          <h3 className="font-semibold text-slate-800 mb-4 flex items-center gap-2">
            <TrendingUp className="w-5 h-5 text-[#FF6800]" />
            Popular Searches
          </h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
            {popularQueries.map((query, index) => (
              <Button
                key={index}
                variant="outline"
                size="sm"
                onClick={() => onSuggestionClick(query)}
                className="justify-start text-left h-auto py-2 px-3 hover:border-[#FF6800] hover:text-[#FF6800] transition-colors"
              >
                <span className="truncate">{query}</span>
                <ArrowRight className="w-3 h-3 ml-auto flex-shrink-0" />
              </Button>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Available Topics */}
      {knowledgeStats && knowledgeStats.topics.length > 0 && (
        <Card className="border-slate-200 shadow-sm">
          <CardContent className="p-6">
            <h3 className="font-semibold text-slate-800 mb-4 flex items-center gap-2">
              <BookOpen className="w-5 h-5 text-[#FF6800]" />
              Available Topics ({knowledgeStats.topics.length})
            </h3>
            <div className="flex flex-wrap gap-2">
              {knowledgeStats.topics.slice(0, 12).map((topic, index) => (
                <Badge
                  key={index}
                  variant="secondary"
                  className="cursor-pointer hover:bg-[#FF6800] hover:text-white transition-colors"
                  onClick={() => onSuggestionClick(topic)}
                >
                  {topic}
                </Badge>
              ))}
              {knowledgeStats.topics.length > 12 && (
                <Badge variant="outline" className="text-slate-500">
                  +{knowledgeStats.topics.length - 12} more topics
                </Badge>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Knowledge Base Stats */}
      {knowledgeStats && (
        <Card className="border-slate-200 shadow-sm bg-gradient-to-r from-blue-50 to-indigo-50">
          <CardContent className="p-6">
            <h3 className="font-semibold text-slate-800 mb-4 flex items-center gap-2">
              <GraduationCap className="w-5 h-5 text-blue-600" />
              Knowledge Base Overview
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
              <div>
                <div className="text-2xl font-bold text-blue-600">{knowledgeStats.totalDocuments}</div>
                <div className="text-sm text-slate-600">Documents</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-green-600">{knowledgeStats.topics.length}</div>
                <div className="text-sm text-slate-600">Topics</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-purple-600">{knowledgeStats.sources.length}</div>
                <div className="text-sm text-slate-600">Sources</div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Quick Actions */}
      <Card className="border-slate-200 shadow-sm">
        <CardContent className="p-6">
          <h3 className="font-semibold text-slate-800 mb-4 flex items-center gap-2">
            <Globe className="w-5 h-5 text-[#FF6800]" />
            Quick Actions
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            <Button
              variant="outline"
              onClick={() => onSuggestionClick('cara daftar beasiswa LPDP')}
              className="justify-start h-auto py-3 px-4 hover:border-[#FF6800] hover:text-[#FF6800]"
            >
              <div className="text-left">
                <div className="font-medium">How to Apply</div>
                <div className="text-xs text-slate-500">Learn the application process</div>
              </div>
            </Button>
            <Button
              variant="outline"
              onClick={() => onSuggestionClick('syarat dokumen LPDP')}
              className="justify-start h-auto py-3 px-4 hover:border-[#FF6800] hover:text-[#FF6800]"
            >
              <div className="text-left">
                <div className="font-medium">Required Documents</div>
                <div className="text-xs text-slate-500">Check document requirements</div>
              </div>
            </Button>
            <Button
              variant="outline"
              onClick={() => onSuggestionClick('jadwal pendaftaran LPDP')}
              className="justify-start h-auto py-3 px-4 hover:border-[#FF6800] hover:text-[#FF6800]"
            >
              <div className="text-left">
                <div className="font-medium">Registration Schedule</div>
                <div className="text-xs text-slate-500">View important dates</div>
              </div>
            </Button>
            <Button
              variant="outline"
              onClick={() => onSuggestionClick('universitas tujuan LPDP')}
              className="justify-start h-auto py-3 px-4 hover:border-[#FF6800] hover:text-[#FF6800]"
            >
              <div className="text-left">
                <div className="font-medium">Target Universities</div>
                <div className="text-xs text-slate-500">Explore university options</div>
              </div>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

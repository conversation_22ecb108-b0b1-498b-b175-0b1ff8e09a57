// Search performance optimization utilities and configurations

export const PERFORMANCE_CONFIG = {
  // Debounce delays for different operations
  SEARCH_DEBOUNCE: 300,
  PRELOAD_DEBOUNCE: 500,
  STATE_UPDATE_DEBOUNCE: 50,
  
  // Cache settings
  CACHE_TTL: 10 * 60 * 1000, // 10 minutes
  MAX_CACHE_SIZE: 100,
  
  // Performance thresholds
  SLOW_SEARCH_THRESHOLD: 2000, // 2 seconds
  VERY_SLOW_SEARCH_THRESHOLD: 5000, // 5 seconds
  FAST_SEARCH_THRESHOLD: 1000, // 1 second
  
  // UI optimization settings
  SKELETON_MIN_DURATION: 300, // Minimum time to show skeleton
  PROGRESS_UPDATE_INTERVAL: 100, // Progress bar update frequency
  ANIMATION_DURATION: 300, // Standard animation duration
  
  // Batch processing settings
  MAX_BATCH_SIZE: 10,
  BATCH_TIMEOUT: 100,
} as const;

// Performance monitoring utilities
export class PerformanceOptimizer {
  private static batchedUpdates: Array<() => void> = [];
  private static batchTimeout: NodeJS.Timeout | null = null;

  // Batch state updates to reduce re-renders
  static batchStateUpdate(updateFn: () => void): void {
    this.batchedUpdates.push(updateFn);
    
    if (this.batchTimeout) {
      clearTimeout(this.batchTimeout);
    }
    
    this.batchTimeout = setTimeout(() => {
      const updates = [...this.batchedUpdates];
      this.batchedUpdates = [];
      this.batchTimeout = null;
      
      // Execute all batched updates
      updates.forEach(update => update());
    }, PERFORMANCE_CONFIG.BATCH_TIMEOUT);
  }

  // Debounce function with performance optimization
  static debounce<T extends (...args: any[]) => any>(
    func: T,
    delay: number,
    immediate = false
  ): (...args: Parameters<T>) => void {
    let timeoutId: NodeJS.Timeout | null = null;
    let lastArgs: Parameters<T>;

    return (...args: Parameters<T>) => {
      lastArgs = args;
      
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
      
      if (immediate && !timeoutId) {
        func(...args);
      }
      
      timeoutId = setTimeout(() => {
        timeoutId = null;
        if (!immediate) {
          func(...lastArgs);
        }
      }, delay);
    };
  }

  // Throttle function for high-frequency events
  static throttle<T extends (...args: any[]) => any>(
    func: T,
    delay: number
  ): (...args: Parameters<T>) => void {
    let lastCall = 0;
    let timeoutId: NodeJS.Timeout | null = null;

    return (...args: Parameters<T>) => {
      const now = Date.now();
      
      if (now - lastCall >= delay) {
        lastCall = now;
        func(...args);
      } else if (!timeoutId) {
        timeoutId = setTimeout(() => {
          lastCall = Date.now();
          timeoutId = null;
          func(...args);
        }, delay - (now - lastCall));
      }
    };
  }

  // Optimize large list rendering
  static shouldRenderItem(index: number, visibleRange: { start: number; end: number }): boolean {
    return index >= visibleRange.start && index <= visibleRange.end;
  }

  // Memory cleanup utilities
  static cleanupResources(resources: Array<() => void>): void {
    resources.forEach(cleanup => {
      try {
        cleanup();
      } catch (error) {
        console.warn('Error during resource cleanup:', error);
      }
    });
  }

  // Performance measurement utilities
  static measurePerformance<T>(
    name: string,
    operation: () => T,
    threshold = PERFORMANCE_CONFIG.SLOW_SEARCH_THRESHOLD
  ): T {
    const start = performance.now();
    const result = operation();
    const duration = performance.now() - start;
    
    if (duration > threshold) {
      console.warn(`⚠️ Slow operation detected: ${name} took ${duration.toFixed(2)}ms`);
    } else if (process.env.NODE_ENV === 'development') {
      console.log(`✅ ${name} completed in ${duration.toFixed(2)}ms`);
    }
    
    return result;
  }

  // Async performance measurement
  static async measureAsyncPerformance<T>(
    name: string,
    operation: () => Promise<T>,
    threshold = PERFORMANCE_CONFIG.SLOW_SEARCH_THRESHOLD
  ): Promise<T> {
    const start = performance.now();
    const result = await operation();
    const duration = performance.now() - start;
    
    if (duration > threshold) {
      console.warn(`⚠️ Slow async operation detected: ${name} took ${duration.toFixed(2)}ms`);
    } else if (process.env.NODE_ENV === 'development') {
      console.log(`✅ ${name} completed in ${duration.toFixed(2)}ms`);
    }
    
    return result;
  }
}

// React performance hooks
export function usePerformanceOptimization() {
  const batchStateUpdate = (updateFn: () => void) => {
    PerformanceOptimizer.batchStateUpdate(updateFn);
  };

  const measureOperation = <T>(name: string, operation: () => T): T => {
    return PerformanceOptimizer.measurePerformance(name, operation);
  };

  const measureAsyncOperation = async <T>(
    name: string, 
    operation: () => Promise<T>
  ): Promise<T> => {
    return PerformanceOptimizer.measureAsyncPerformance(name, operation);
  };

  return {
    batchStateUpdate,
    measureOperation,
    measureAsyncOperation,
    debounce: PerformanceOptimizer.debounce,
    throttle: PerformanceOptimizer.throttle,
  };
}

// Search-specific optimizations
export const SearchOptimizations = {
  // Determine if search should show progress overlay
  shouldShowProgress: (duration: number): boolean => {
    return duration > PERFORMANCE_CONFIG.SKELETON_MIN_DURATION;
  },

  // Determine if search should use skeleton loading
  shouldShowSkeleton: (isLoading: boolean, duration: number): boolean => {
    return isLoading && duration > PERFORMANCE_CONFIG.SKELETON_MIN_DURATION;
  },

  // Calculate optimal batch size for results
  getOptimalBatchSize: (totalResults: number): number => {
    if (totalResults <= 10) return totalResults;
    if (totalResults <= 50) return 10;
    return PERFORMANCE_CONFIG.MAX_BATCH_SIZE;
  },

  // Determine if results should be virtualized
  shouldVirtualizeResults: (resultCount: number): boolean => {
    return resultCount > 50;
  },
} as const;

export default PerformanceOptimizer;

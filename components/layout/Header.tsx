'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  ArrowLeft, 
  Bot,
  Settings,
  Shield,
  LogOut,
  Brain
} from 'lucide-react';
import Link from 'next/link';
import { toast } from 'sonner';
import { AuthService } from '@/lib/auth';

interface HeaderProps {
  title?: string;
  subtitle?: string;
  showBackButton?: boolean;
  backUrl?: string;
}

export default function Header({ 
  title = "Tanya LPDP", 
  subtitle = "Search Results",
  showBackButton = true,
  backUrl = "/"
}: HeaderProps) {
  const [user, setUser] = useState<any>(null);

  useEffect(() => {
    // Check authentication status
    const checkAuth = async () => {
      const currentUser = await AuthService.getCurrentUser();
      setUser(currentUser);
    };
    checkAuth();

    // Listen for auth changes
    const { data: { subscription } } = AuthService.onAuthStateChange((user) => {
      setUser(user);
    });

    return () => subscription.unsubscribe();
  }, []);

  const handleSignOut = async () => {
    try {
      await AuthService.signOut();
      setUser(null);
      toast.success('Successfully signed out!');
    } catch (error) {
      console.error('Sign out error:', error);
      toast.error('Failed to sign out');
    }
  };

  return (
    <div className="border-b border-slate-200 bg-white/80 backdrop-blur-sm sticky top-0 z-10">
      <div className="container mx-auto px-4 py-3 max-w-6xl">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            {showBackButton && (
              <Link href={backUrl}>
                <Button variant="ghost" size="sm" className="text-slate-600 hover:text-slate-800">
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Back
                </Button>
              </Link>
            )}
            <div className="flex items-center gap-3">
              <div className="p-2 bg-[#FF6800] rounded-lg">
                <Bot className="w-6 h-6 text-white" />
              </div>
              <div>
                <h1 className="text-xl font-bold text-slate-800">{title}</h1>
                <p className="text-slate-600 text-sm flex items-center gap-2">
                  <Brain className="w-3 h-3" />
                  {subtitle}
                </p>
              </div>
            </div>
          </div>
          <div className="flex items-center gap-2">
            {user ? (
              <>
                <Badge variant="outline" className="hidden sm:flex items-center gap-1 text-xs">
                  <Shield className="w-3 h-3" />
                  Authenticated
                </Badge>
                <Link href="/admin">
                  <Button variant="outline" size="sm">
                    <Settings className="w-4 h-4 mr-2" />
                    Admin Panel
                  </Button>
                </Link>
                <Button variant="outline" size="sm" onClick={handleSignOut}>
                  <LogOut className="w-4 h-4 mr-2" />
                  Sign Out
                </Button>
              </>
            ) : null}
          </div>
        </div>
      </div>
    </div>
  );
}

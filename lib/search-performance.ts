// Search performance monitoring and optimization utilities

interface PerformanceMetrics {
  searchStartTime: number;
  searchEndTime: number;
  cacheHit: boolean;
  resultCount: number;
  queryLength: number;
  searchStrategies: string[];
  databaseStorageTime?: number;
  routeTransitionTime?: number;
  totalResponseTime: number;
  apiCallTime?: number;
  vectorSearchTime?: number;
  embeddingGenerationTime?: number;
  databaseQueryTime?: number;
}

interface DetailedTiming {
  apiStart?: number;
  apiEnd?: number;
  vectorSearchStart?: number;
  vectorSearchEnd?: number;
  embeddingStart?: number;
  embeddingEnd?: number;
  databaseStart?: number;
  databaseEnd?: number;
}

interface SearchPerformanceData {
  query: string;
  metrics: PerformanceMetrics;
  timestamp: number;
  userAgent?: string;
}

class SearchPerformanceMonitor {
  private metrics: SearchPerformanceData[] = [];
  private readonly MAX_METRICS = 100; // Keep last 100 searches for analysis
  private currentSearch: Partial<PerformanceMetrics> = {};
  private currentTiming: DetailedTiming = {};

  // Start tracking a search operation
  startSearch(query: string): void {
    const now = performance.now();
    this.currentSearch = {
      searchStartTime: now,
      queryLength: query.length,
      searchStrategies: [],
      cacheHit: false
    };
    this.currentTiming = {
      apiStart: now
    };
  }

  // Mark cache hit
  markCacheHit(): void {
    this.currentSearch.cacheHit = true;
  }

  // Add search strategy used
  addSearchStrategy(strategy: string): void {
    if (!this.currentSearch.searchStrategies) {
      this.currentSearch.searchStrategies = [];
    }
    this.currentSearch.searchStrategies.push(strategy);
  }

  // Mark database storage time
  markDatabaseStorage(startTime: number): void {
    this.currentSearch.databaseStorageTime = performance.now() - startTime;
  }

  // Mark route transition time
  markRouteTransition(startTime: number): void {
    this.currentSearch.routeTransitionTime = performance.now() - startTime;
  }

  // Mark API call completion
  markApiComplete(): void {
    this.currentTiming.apiEnd = performance.now();
  }

  // Mark vector search timing
  markVectorSearchStart(): void {
    this.currentTiming.vectorSearchStart = performance.now();
  }

  markVectorSearchEnd(): void {
    this.currentTiming.vectorSearchEnd = performance.now();
  }

  // Mark embedding generation timing
  markEmbeddingStart(): void {
    this.currentTiming.embeddingStart = performance.now();
  }

  markEmbeddingEnd(): void {
    this.currentTiming.embeddingEnd = performance.now();
  }

  // Mark database query timing
  markDatabaseStart(): void {
    this.currentTiming.databaseStart = performance.now();
  }

  markDatabaseEnd(): void {
    this.currentTiming.databaseEnd = performance.now();
  }

  // End search tracking
  endSearch(query: string, resultCount: number): PerformanceMetrics | null {
    if (!this.currentSearch.searchStartTime) {
      console.warn('Search tracking not started properly');
      return null;
    }

    const endTime = performance.now();

    // Calculate detailed timings
    const apiCallTime = this.currentTiming.apiEnd && this.currentTiming.apiStart
      ? this.currentTiming.apiEnd - this.currentTiming.apiStart : undefined;
    const vectorSearchTime = this.currentTiming.vectorSearchEnd && this.currentTiming.vectorSearchStart
      ? this.currentTiming.vectorSearchEnd - this.currentTiming.vectorSearchStart : undefined;
    const embeddingGenerationTime = this.currentTiming.embeddingEnd && this.currentTiming.embeddingStart
      ? this.currentTiming.embeddingEnd - this.currentTiming.embeddingStart : undefined;
    const databaseQueryTime = this.currentTiming.databaseEnd && this.currentTiming.databaseStart
      ? this.currentTiming.databaseEnd - this.currentTiming.databaseStart : undefined;

    const metrics: PerformanceMetrics = {
      searchStartTime: this.currentSearch.searchStartTime,
      searchEndTime: endTime,
      totalResponseTime: endTime - this.currentSearch.searchStartTime,
      cacheHit: this.currentSearch.cacheHit || false,
      resultCount,
      queryLength: this.currentSearch.queryLength || 0,
      searchStrategies: this.currentSearch.searchStrategies || [],
      databaseStorageTime: this.currentSearch.databaseStorageTime,
      routeTransitionTime: this.currentSearch.routeTransitionTime,
      apiCallTime,
      vectorSearchTime,
      embeddingGenerationTime,
      databaseQueryTime
    };

    // Store metrics
    this.metrics.push({
      query,
      metrics,
      timestamp: Date.now(),
      userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : undefined
    });

    // Keep only recent metrics
    if (this.metrics.length > this.MAX_METRICS) {
      this.metrics = this.metrics.slice(-this.MAX_METRICS);
    }

    // Reset current search and timing
    this.currentSearch = {};
    this.currentTiming = {};

    // Log performance in development
    if (process.env.NODE_ENV === 'development') {
      this.logPerformance(metrics);
    }

    return metrics;
  }

  // Log performance metrics with detailed breakdown (async to avoid blocking main thread)
  private logPerformance(metrics: PerformanceMetrics): void {
    // Use setTimeout to defer logging to avoid blocking the main thread
    setTimeout(() => {
      const {
        totalResponseTime,
        cacheHit,
        resultCount,
        searchStrategies,
        databaseStorageTime,
        routeTransitionTime,
        apiCallTime,
        vectorSearchTime,
        embeddingGenerationTime,
        databaseQueryTime
      } = metrics;

      // Only log detailed metrics if performance is concerning
      if (totalResponseTime > 1000) {
        console.group('🔍 Search Performance Metrics');
        console.log(`⏱️  Total Response Time: ${totalResponseTime.toFixed(2)}ms`);
        console.log(`💾 Cache Hit: ${cacheHit ? '✅ Yes' : '❌ No'}`);
        console.log(`📊 Results Found: ${resultCount}`);
        console.log(`🔧 Search Strategies: ${searchStrategies.join(', ')}`);

        // Detailed timing breakdown
        if (apiCallTime) {
          console.log(`🌐 API Call Time: ${apiCallTime.toFixed(2)}ms`);
        }

        if (embeddingGenerationTime) {
          console.log(`🧠 Embedding Generation: ${embeddingGenerationTime.toFixed(2)}ms`);
        }

        if (vectorSearchTime) {
          console.log(`🔍 Vector Search: ${vectorSearchTime.toFixed(2)}ms`);
        }

        if (databaseQueryTime) {
          console.log(`💾 Database Query: ${databaseQueryTime.toFixed(2)}ms`);
        }

        if (databaseStorageTime) {
          console.log(`💽 Database Storage: ${databaseStorageTime.toFixed(2)}ms`);
        }

        if (routeTransitionTime) {
          console.log(`🔄 Route Transition: ${routeTransitionTime.toFixed(2)}ms`);
        }

        // Performance warnings with more context
        if (totalResponseTime > 2000) {
          console.warn('⚠️  Slow search detected (>2s)');

          // Identify bottlenecks
          if (apiCallTime && apiCallTime > 1500) {
            console.warn('   🌐 API call is the primary bottleneck');
          }
          if (embeddingGenerationTime && embeddingGenerationTime > 1000) {
            console.warn('   🧠 Embedding generation is slow');
          }
          if (vectorSearchTime && vectorSearchTime > 500) {
            console.warn('   🔍 Vector search is slow');
          }
          if (databaseQueryTime && databaseQueryTime > 500) {
            console.warn('   💾 Database queries are slow');
          }
        }

        if (totalResponseTime > 5000) {
          console.error('🚨 Very slow search detected (>5s)');
          console.error('   Consider investigating the bottlenecks identified above');
        }

        console.groupEnd();
      } else {
        // For fast searches, just log a simple message
        console.log(`🔍 Search completed in ${totalResponseTime.toFixed(2)}ms (${cacheHit ? 'cache hit' : 'fresh'})`);
      }
    }, 0);
  }

  // Get performance statistics
  getPerformanceStats(): {
    averageResponseTime: number;
    cacheHitRate: number;
    totalSearches: number;
    slowSearches: number;
    averageResultCount: number;
  } {
    if (this.metrics.length === 0) {
      return {
        averageResponseTime: 0,
        cacheHitRate: 0,
        totalSearches: 0,
        slowSearches: 0,
        averageResultCount: 0
      };
    }

    const totalResponseTime = this.metrics.reduce(
      (sum, data) => sum + data.metrics.totalResponseTime, 0
    );
    const cacheHits = this.metrics.filter(data => data.metrics.cacheHit).length;
    const slowSearches = this.metrics.filter(
      data => data.metrics.totalResponseTime > 2000
    ).length;
    const totalResults = this.metrics.reduce(
      (sum, data) => sum + data.metrics.resultCount, 0
    );

    return {
      averageResponseTime: totalResponseTime / this.metrics.length,
      cacheHitRate: (cacheHits / this.metrics.length) * 100,
      totalSearches: this.metrics.length,
      slowSearches,
      averageResultCount: totalResults / this.metrics.length
    };
  }

  // Get recent performance data
  getRecentMetrics(count: number = 10): SearchPerformanceData[] {
    return this.metrics.slice(-count);
  }

  // Clear all metrics
  clearMetrics(): void {
    this.metrics = [];
  }

  // Export metrics for analysis
  exportMetrics(): string {
    return JSON.stringify(this.metrics, null, 2);
  }
}

// Global performance monitor instance
export const searchPerformanceMonitor = new SearchPerformanceMonitor();

// Performance optimization utilities
export class SearchOptimizer {
  // Determine if search should use cache
  static shouldUseCache(query: string): boolean {
    // Use cache for queries longer than 2 characters
    return query.trim().length >= 2;
  }

  // Determine if search should be preloaded
  static shouldPreload(query: string): boolean {
    // Preload for queries longer than 3 characters
    return query.trim().length >= 3;
  }

  // Determine optimal search strategies based on query
  static getOptimalStrategies(query: string): string[] {
    const strategies: string[] = [];
    
    // Always include vector search
    strategies.push('vector');
    
    // Add topic search for longer queries
    if (query.length >= 5) {
      strategies.push('topic');
    }
    
    // Add keyword search for specific terms
    if (query.includes(' ') || query.length >= 8) {
      strategies.push('keyword');
    }
    
    return strategies;
  }

  // Calculate search priority score
  static calculatePriority(query: string, cacheHit: boolean): number {
    let priority = 1;
    
    // Higher priority for cache hits
    if (cacheHit) priority += 2;
    
    // Higher priority for longer, more specific queries
    if (query.length >= 10) priority += 1;
    if (query.includes(' ')) priority += 1;
    
    return priority;
  }
}

// Hook for React components to track search performance
export function useSearchPerformance() {
  const startSearch = (query: string) => {
    searchPerformanceMonitor.startSearch(query);
  };

  const endSearch = (query: string, resultCount: number) => {
    return searchPerformanceMonitor.endSearch(query, resultCount);
  };

  const markCacheHit = () => {
    searchPerformanceMonitor.markCacheHit();
  };

  const markApiComplete = () => {
    searchPerformanceMonitor.markApiComplete();
  };

  const markVectorSearchStart = () => {
    searchPerformanceMonitor.markVectorSearchStart();
  };

  const markVectorSearchEnd = () => {
    searchPerformanceMonitor.markVectorSearchEnd();
  };

  const markEmbeddingStart = () => {
    searchPerformanceMonitor.markEmbeddingStart();
  };

  const markEmbeddingEnd = () => {
    searchPerformanceMonitor.markEmbeddingEnd();
  };

  const markDatabaseStart = () => {
    searchPerformanceMonitor.markDatabaseStart();
  };

  const markDatabaseEnd = () => {
    searchPerformanceMonitor.markDatabaseEnd();
  };

  const getStats = () => {
    return searchPerformanceMonitor.getPerformanceStats();
  };

  return {
    startSearch,
    endSearch,
    markCacheHit,
    markApiComplete,
    markVectorSearchStart,
    markVectorSearchEnd,
    markEmbeddingStart,
    markEmbeddingEnd,
    markDatabaseStart,
    markDatabaseEnd,
    getStats
  };
}

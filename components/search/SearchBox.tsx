'use client';

import { useState, useRef, useEffect, KeyboardEvent } from 'react';
import { Button } from '@/components/ui/button';
import { Search } from 'lucide-react';
import { Textarea } from '@/components/ui/textarea';

interface SearchBoxProps {
  onSearch: (query: string) => void;
  placeholder?: string;
  isLoading?: boolean;
  initialValue?: string;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export default function SearchBox({ 
  onSearch, 
  placeholder = "Search...", 
  isLoading = false,
  initialValue = '',
  size = 'md',
  className = ''
}: SearchBoxProps) {
  const [query, setQuery] = useState(initialValue);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Adjust textarea height based on content
  const adjustTextareaHeight = () => {
    const textarea = textareaRef.current;
    if (textarea) {
      // Reset height to get the correct scrollHeight
      textarea.style.height = 'auto';
      // Set the height to scrollHeight, but not more than 200px
      textarea.style.height = `${Math.min(textarea.scrollHeight, 200)}px`;
    }
  };

  // Adjust height when query changes
  useEffect(() => {
    adjustTextareaHeight();
  }, [query]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (query.trim()) {
      onSearch(query.trim());
    }
  };

  const handleKeyDown = (e: KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      if (query.trim()) {
        onSearch(query.trim());
      }
    }
  };

  const sizeClasses = {
    sm: {
      input: 'pl-10 pr-4 py-2 text-sm',
      button: 'px-4 py-1',
      icon: 'w-4 h-4',
      iconPosition: 'left-3'
    },
    md: {
      input: 'pl-12 pr-4 py-3 text-base',
      button: 'px-4 py-1',
      icon: 'w-5 h-5',
      iconPosition: 'left-4'
    },
    lg: {
      input: 'pl-12 pr-4 py-4 text-lg',
      button: 'px-6 py-2',
      icon: 'w-5 h-5',
      iconPosition: 'left-4'
    }
  };

  const currentSize = sizeClasses[size];

  return (
    <form onSubmit={handleSubmit} className={`relative ${className}`}>
      <div className="relative">
        <Textarea
          ref={textareaRef}
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          onKeyDown={handleKeyDown}
          placeholder={placeholder}
          className="min-h-[60px] w-full rounded-2xl border-2 border-slate-200 p-4 pl-12 pr-24 text-base focus:border-[#FF6800] focus:ring-0 focus:ring-offset-0 focus-visible:ring-0 focus-visible:ring-offset-0 shadow-lg resize-none overflow-hidden outline-none"
          style={{ 
            minHeight: '60px',
            maxHeight: '200px',
            lineHeight: '1.5',
            boxSizing: 'border-box',
            transition: 'none',
            outline: 'none',
          }}
          disabled={isLoading}
        />
        <Search className="absolute left-4 top-4 text-slate-400 w-5 h-5" />
        <Button
          type="submit"
          disabled={!query.trim() || isLoading}
          className="absolute right-2 bottom-2 bg-[#FF6800] hover:bg-[#e55a00] rounded-lg px-4 py-2 h-10 flex items-center justify-center"
        >
          {isLoading ? (
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
              {size === 'lg' && <span>Searching</span>}
            </div>
          ) : (
            <span>{size === 'lg' ? 'Search' : 'Go'}</span>
          )}
        </Button>
      </div>
    </form>
  );
}

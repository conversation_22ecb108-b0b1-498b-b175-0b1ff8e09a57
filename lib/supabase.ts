import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
const supabaseServiceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl) {
  throw new Error('Missing NEXT_PUBLIC_SUPABASE_URL environment variable')
}

if (!supabaseAnonKey) {
  throw new Error('Missing NEXT_PUBLIC_SUPABASE_ANON_KEY environment variable')
}

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Server-side client with service role key for admin operations
export const supabaseAdmin = createClient(
  supabaseUrl,
  supabaseServiceRoleKey || supabaseAnonKey,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
)

// Create an authenticated client with a user token
export function createAuthenticatedClient(accessToken: string) {
  if (!supabaseUrl || !supabaseAnonKey) {
    throw new Error('Supabase URL or Anon Key is not properly configured');
  }
  
  return createClient(supabaseUrl, supabaseAnonKey, {
    global: {
      headers: {
        Authorization: `Bearer ${accessToken}`
      }
    },
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  })
}

export type Database = {
  public: {
    Tables: {
      knowledge_sources: {
        Row: {
          id: string
          title: string
          type: 'pdf' | 'url'
          source: string
          source_url: string | null
          content: string
          topics: string[]
          status: 'active' | 'processing' | 'error'
          file_path: string | null
          file_size: number | null
          file_name: string | null
          created_at: string
          updated_at: string
          created_by: string
        }
        Insert: {
          id?: string
          title: string
          type: 'pdf' | 'url'
          source: string
          source_url?: string | null
          content: string
          topics?: string[]
          status?: 'active' | 'processing' | 'error'
          file_path?: string | null
          file_size?: number | null
          file_name?: string | null
          created_at?: string
          updated_at?: string
          created_by: string
        }
        Update: {
          id?: string
          title?: string
          type?: 'pdf' | 'url'
          source?: string
          source_url?: string | null
          content?: string
          topics?: string[]
          status?: 'active' | 'processing' | 'error'
          file_path?: string | null
          file_size?: number | null
          file_name?: string | null
          created_at?: string
          updated_at?: string
          created_by?: string
        }
      }
      document_embeddings: {
        Row: {
          id: string
          source_id: string
          content_chunk: string
          embedding: number[]
          chunk_index: number
          created_at: string
        }
        Insert: {
          id?: string
          source_id: string
          content_chunk: string
          embedding: number[]
          chunk_index: number
          created_at?: string
        }
        Update: {
          id?: string
          source_id?: string
          content_chunk?: string
          embedding?: number[]
          chunk_index?: number
          created_at?: string
        }
      }
      chat_sessions: {
        Row: {
          id: string
          user_id: string
          title: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          title?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          title?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      chat_messages: {
        Row: {
          id: string
          session_id: string
          content: string
          role: 'user' | 'assistant'
          sources: string[] | null
          source_urls: Record<string, string> | null
          from_knowledge_base: boolean
          embedding_type: string | null
          user_id: string | null
          metadata: Record<string, any> | null
          created_at: string
        }
        Insert: {
          id?: string
          session_id: string
          content: string
          role: 'user' | 'assistant'
          sources?: string[] | null
          source_urls?: Record<string, string> | null
          from_knowledge_base?: boolean
          embedding_type?: string | null
          user_id?: string | null
          metadata?: Record<string, any> | null
          created_at?: string
        }
        Update: {
          id?: string
          session_id?: string
          content?: string
          role?: 'user' | 'assistant'
          sources?: string[] | null
          source_urls?: Record<string, string> | null
          from_knowledge_base?: boolean
          embedding_type?: string | null
          user_id?: string | null
          metadata?: Record<string, any> | null
          created_at?: string
        }
      }
      search_results: {
        Row: {
          id: string
          query: string
          results: Record<string, any>
          user_id: string | null
          expires_at: string
          created_at: string
          accessed_count: number
          last_accessed_at: string | null
        }
        Insert: {
          id?: string
          query: string
          results: Record<string, any>
          user_id?: string | null
          expires_at?: string
          created_at?: string
          accessed_count?: number
          last_accessed_at?: string | null
        }
        Update: {
          id?: string
          query?: string
          results?: Record<string, any>
          user_id?: string | null
          expires_at?: string
          created_at?: string
          accessed_count?: number
          last_accessed_at?: string | null
        }
      }
    }
  }
}
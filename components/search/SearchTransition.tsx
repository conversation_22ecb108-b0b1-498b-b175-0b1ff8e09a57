'use client';

import { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';

interface SearchTransitionProps {
  children: React.ReactNode;
  isTransitioning?: boolean;
  onTransitionComplete?: () => void;
}

export default function SearchTransition({ 
  children, 
  isTransitioning = false,
  onTransitionComplete 
}: SearchTransitionProps) {
  const [isVisible, setIsVisible] = useState(true);
  const [isAnimating, setIsAnimating] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (isTransitioning) {
      setIsAnimating(true);
      setIsVisible(false);
      
      const timer = setTimeout(() => {
        setIsAnimating(false);
        onTransitionComplete?.();
      }, 300);

      return () => clearTimeout(timer);
    } else {
      setIsVisible(true);
      setIsAnimating(false);
    }
  }, [isTransitioning, onTransitionComplete]);

  return (
    <div 
      ref={containerRef}
      className={`transition-all duration-300 ease-in-out ${
        isVisible 
          ? 'opacity-100 translate-y-0' 
          : 'opacity-0 translate-y-2'
      } ${isAnimating ? 'pointer-events-none' : ''}`}
    >
      {children}
      
      {/* Transition Overlay */}
      {isTransitioning && (
        <div className="fixed inset-0 bg-white/80 backdrop-blur-sm z-50 flex items-center justify-center">
          <div className="text-center">
            <div className="w-8 h-8 border-4 border-[#FF6800] border-t-transparent rounded-full animate-spin mx-auto mb-4" />
            <p className="text-slate-600">Preparing search results...</p>
          </div>
        </div>
      )}
    </div>
  );
}

// Hook for managing search transitions
export function useSearchTransition() {
  const [isTransitioning, setIsTransitioning] = useState(false);
  const router = useRouter();

  const startTransition = (targetUrl: string, delay: number = 0) => {
    setIsTransitioning(true);
    
    setTimeout(() => {
      router.push(targetUrl);
    }, delay);
  };

  const completeTransition = () => {
    setIsTransitioning(false);
  };

  return {
    isTransitioning,
    startTransition,
    completeTransition
  };
}

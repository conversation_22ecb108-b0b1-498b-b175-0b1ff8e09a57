/**
 * Test script for Shareable Search URLs feature
 * 
 * This script tests the complete flow:
 * 1. Perform a search and get a UUID
 * 2. Retrieve the search results using the UUID
 * 3. Verify the results match the original search
 * 4. Test error handling for invalid/expired UUIDs
 */

const BASE_URL = 'http://localhost:3001';

async function testShareableUrls() {
  console.log('🧪 Testing Shareable Search URLs Feature\n');

  try {
    // Test 1: Perform a search and get UUID
    console.log('1️⃣ Testing search with UUID generation...');
    const searchResponse = await fetch(`${BASE_URL}/api/search`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ query: 'LPDP scholarship test' }),
    });

    if (!searchResponse.ok) {
      throw new Error(`Search failed: ${searchResponse.status}`);
    }

    const searchData = await searchResponse.json();
    console.log(`✅ Search completed successfully`);
    console.log(`📊 Results: ${searchData.totalResults} found`);
    console.log(`🔗 UUID: ${searchData.uuid}`);

    if (!searchData.uuid) {
      throw new Error('No UUID returned from search');
    }

    // Test 2: Retrieve results using UUID
    console.log('\n2️⃣ Testing UUID-based result retrieval...');
    const uuidResponse = await fetch(`${BASE_URL}/api/search/results/${searchData.uuid}`);

    if (!uuidResponse.ok) {
      throw new Error(`UUID retrieval failed: ${uuidResponse.status}`);
    }

    const uuidData = await uuidResponse.json();
    console.log(`✅ UUID retrieval successful`);
    console.log(`📊 Retrieved results: ${uuidData.totalResults} found`);
    console.log(`🔍 Original query: "${uuidData.originalQuery}"`);
    console.log(`👀 Access count: ${uuidData.accessCount}`);

    // Test 3: Verify data consistency
    console.log('\n3️⃣ Testing data consistency...');
    if (searchData.query !== uuidData.originalQuery) {
      throw new Error('Query mismatch between original and retrieved results');
    }
    if (searchData.totalResults !== uuidData.totalResults) {
      throw new Error('Result count mismatch between original and retrieved results');
    }
    console.log('✅ Data consistency verified');

    // Test 4: Test invalid UUID
    console.log('\n4️⃣ Testing invalid UUID handling...');
    const invalidUuid = '00000000-0000-0000-0000-000000000000';
    const invalidResponse = await fetch(`${BASE_URL}/api/search/results/${invalidUuid}`);
    
    if (invalidResponse.status !== 404) {
      console.log(`⚠️  Expected 404 for invalid UUID, got ${invalidResponse.status}`);
    } else {
      console.log('✅ Invalid UUID properly handled with 404');
    }

    // Test 5: Test malformed UUID
    console.log('\n5️⃣ Testing malformed UUID handling...');
    const malformedUuid = 'not-a-uuid';
    const malformedResponse = await fetch(`${BASE_URL}/api/search/results/${malformedUuid}`);
    
    if (malformedResponse.status !== 400) {
      console.log(`⚠️  Expected 400 for malformed UUID, got ${malformedResponse.status}`);
    } else {
      console.log('✅ Malformed UUID properly handled with 400');
    }

    // Test 6: Test access tracking
    console.log('\n6️⃣ Testing access tracking...');
    const secondAccessResponse = await fetch(`${BASE_URL}/api/search/results/${searchData.uuid}`);
    const secondAccessData = await secondAccessResponse.json();
    
    if (secondAccessData.accessCount > uuidData.accessCount) {
      console.log(`✅ Access tracking working (count increased from ${uuidData.accessCount} to ${secondAccessData.accessCount})`);
    } else {
      console.log(`⚠️  Access tracking may not be working properly`);
    }

    console.log('\n🎉 All tests completed successfully!');
    console.log('\n📋 Test Summary:');
    console.log(`   ✅ Search with UUID generation`);
    console.log(`   ✅ UUID-based result retrieval`);
    console.log(`   ✅ Data consistency verification`);
    console.log(`   ✅ Invalid UUID error handling`);
    console.log(`   ✅ Malformed UUID error handling`);
    console.log(`   ✅ Access tracking functionality`);

    // Display shareable URL for manual testing
    console.log('\n🔗 Shareable URL for manual testing:');
    console.log(`   ${BASE_URL}/search/results/${searchData.uuid}`);
    console.log('\n💡 You can copy this URL and open it in a browser to test the UI!');

  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    process.exit(1);
  }
}

// Run the test
if (require.main === module) {
  testShareableUrls();
}

module.exports = { testShareableUrls };

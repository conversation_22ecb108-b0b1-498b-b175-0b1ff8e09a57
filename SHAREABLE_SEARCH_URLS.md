# Shareable Search URLs Feature

## Overview

This feature enables users to generate and share unique URLs for search results, allowing others to view the exact same search results without re-running the search query. This works similar to how Google search results can be shared via URL.

## How It Works

### 1. Search Result Storage
- When a user performs a search, the results are automatically stored in the `search_results` table with a unique UUID
- Each stored result includes:
  - Unique UUID identifier
  - Original search query
  - Complete search results (JSON)
  - User ID (if authenticated, null for anonymous)
  - Expiration timestamp (24 hours from creation)
  - Access tracking (count and last accessed time)

### 2. URL Generation
- After search completion, users are automatically redirected to: `/search/results/{uuid}`
- The UUID-based URL can be shared with others
- Recipients can access the exact same search results without re-running the search

### 3. Access and Sharing
- **Share Button**: Available on search results pages to copy the shareable URL
- **Public Access**: Anyone with the URL can view the results (no authentication required)
- **Expiration**: Results expire after 24 hours to prevent unlimited storage growth
- **Access Tracking**: View count and last access time are tracked

## API Endpoints

### POST /api/search
- **Enhanced**: Now stores search results and returns UUID
- **Response**: Includes `uuid` field for the shareable URL
- **Automatic Redirect**: Client automatically redirects to UUID-based URL

### GET /api/search/results/[uuid]
- **Purpose**: Retrieve stored search results by UUID
- **Validation**: Checks UUID format and expiration
- **Access Tracking**: Increments view count and updates last accessed time
- **Error Handling**: Returns 404 for expired or invalid UUIDs

### POST /api/search/cleanup
- **Purpose**: Remove expired search results (for maintenance)
- **Usage**: Can be called manually or via cron job
- **Response**: Returns count of deleted results

## Database Schema

### search_results Table
```sql
CREATE TABLE search_results (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  query TEXT NOT NULL,
  results JSONB NOT NULL,
  user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  expires_at TIMESTAMPTZ NOT NULL DEFAULT (NOW() + INTERVAL '24 hours'),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  accessed_count INTEGER DEFAULT 0,
  last_accessed_at TIMESTAMPTZ
);
```

### Row Level Security (RLS)
- **Read Access**: Anyone can read non-expired results (enables sharing)
- **Create Access**: Both authenticated and anonymous users can create results
- **Update Access**: Users can update their own results (for access tracking)

## User Experience

### Search Flow
1. User performs search on `/search?q=query`
2. Search API processes query and stores results with UUID
3. User is automatically redirected to `/search/results/{uuid}`
4. User can share the UUID-based URL with others

### Shared Results Page
- Shows original search query and results
- Displays "Shared Search Results" indicator
- Shows view count and creation date
- Includes share button for easy URL copying
- Allows new searches from the same page

### Error Handling
- **Invalid UUID**: Returns 400 with clear error message
- **Expired Results**: Returns 404 with explanation and suggestion to search again
- **Missing Results**: Returns 404 with fallback to new search

## Security Considerations

### Data Privacy
- No sensitive user data is stored in search results
- User association is optional (anonymous sharing supported)
- Results automatically expire after 24 hours

### Access Control
- Public read access enables sharing without authentication
- No sensitive operations exposed through shared URLs
- Rate limiting applies to search creation, not result viewing

### Storage Management
- 24-hour expiration prevents unlimited growth
- Cleanup endpoint available for maintenance
- Efficient indexing on expiration timestamp

## Implementation Files

### Core Components
- `app/api/search/route.ts` - Enhanced search API with storage
- `app/api/search/results/[uuid]/route.ts` - UUID-based result retrieval
- `app/search/results/[uuid]/page.tsx` - Shared results page
- `components/search/SearchResultsContent.tsx` - Shared results component
- `components/search/SearchResults.tsx` - Reusable results display

### Database
- `supabase/migrations/20250615_add_search_results_storage.sql` - Table creation
- `lib/supabase.ts` - Updated TypeScript types

### Utilities
- `app/api/search/cleanup/route.ts` - Cleanup endpoint
- `components/layout/Header.tsx` - Reusable header component

## Usage Examples

### Sharing a Search Result
1. Search for "LPDP scholarship requirements"
2. Get redirected to `/search/results/123e4567-e89b-12d3-a456-************`
3. Copy and share the URL with others
4. Recipients see the exact same search results

### Direct Access
- URL: `https://yourapp.com/search/results/123e4567-e89b-12d3-a456-************`
- Shows original query, results, AI summary, and metadata
- Includes share button and option to perform new search

## Maintenance

### Cleanup Expired Results
```bash
# Manual cleanup
curl -X POST https://yourapp.com/api/search/cleanup

# Or set up a cron job to run daily
0 2 * * * curl -X POST https://yourapp.com/api/search/cleanup
```

### Monitoring
- Track storage usage via database queries
- Monitor access patterns through `accessed_count` field
- Review expiration effectiveness through cleanup logs

## Future Enhancements

### Potential Improvements
- **Custom Expiration**: Allow users to set custom expiration times
- **Result Collections**: Group related searches into collections
- **Analytics**: Track popular search patterns and shared results
- **Bookmarking**: Allow users to save important search results
- **Export Options**: PDF or other format exports of search results
